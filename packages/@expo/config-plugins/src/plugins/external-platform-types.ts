/**
 * TypeScript module declaration to extend ModConfig for external platforms.
 * This allows external platforms to add their own mod configurations using declaration merging.
 */

import { Mod } from '../Plugin.types';

/**
 * External platform mod configuration interface.
 * External platforms can extend this using TypeScript declaration merging.
 * 
 * Example:
 * ```typescript
 * declare module '@expo/config-plugins' {
 *   interface ModConfig {
 *     myplatform?: ExternalPlatformModConfig & {
 *       customMod?: Mod<MyCustomType>;
 *     };
 *   }
 * }
 * ```
 */
export interface ExternalPlatformModConfig {
  /** Dangerously make a modification before any other platform mods have been run. */
  dangerous?: Mod<unknown>;
  /** Dangerously make a modification after all the other platform mods have been run. */
  finalized?: Mod<unknown>;
  /** Platform-specific manifest or configuration file */
  manifest?: Mod<any>;
  /** Platform-specific configuration file */
  config?: Mod<any>;
  /** Platform-specific project file */
  project?: Mod<any>;
  /** Platform-specific build configuration */
  build?: Mod<any>;
  /** Platform-specific resources */
  resources?: Mod<any>;
  /** Platform-specific assets */
  assets?: Mod<any>;
  /** Platform-specific dependencies */
  dependencies?: Mod<any>;
  /** Platform-specific permissions */
  permissions?: Mod<any>;
  /** Platform-specific entitlements */
  entitlements?: Mod<any>;
  /** Platform-specific metadata */
  metadata?: Mod<any>;
}

// Note: External platforms should extend ModConfig in their own packages like this:
//
// declare module '@expo/config-plugins' {
//   interface ModConfig {
//     myplatform?: ExternalPlatformModConfig & {
//       // Add platform-specific mods here
//       myCustomMod?: Mod<MyCustomType>;
//     };
//   }
// }
//
// This approach ensures that:
// 1. External platforms can add their own mod types
// 2. TypeScript will properly type-check platform-specific mods
// 3. The core ModConfig interface remains clean
// 4. Multiple external platforms can coexist without conflicts
