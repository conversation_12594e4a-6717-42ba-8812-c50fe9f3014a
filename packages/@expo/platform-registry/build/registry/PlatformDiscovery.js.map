{"version": 3, "file": "PlatformDiscovery.js", "sourceRoot": "", "sources": ["../../src/registry/PlatformDiscovery.ts"], "names": [], "mappings": ";;;AAEA,mDAAwD;AAExD;;;GAGG;AACH,MAAa,iBAAiB;IAG5B;;;OAGG;IACK,MAAM,CAAC,yBAAyB,CACtC,WAAoB,EACpB,UAAmB,IAAI,EACvB,QAA2B;QAE3B,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAuB,CAAC;QAEhF,MAAM,mBAAmB,GAAG,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACzD,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;QAErE,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;YAChC,KAAK,CAAC,qCAAqB,CAAC,wBAAwB,EAAE,CAAC,CAAC;YACxD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;SAChD;QAED,MAAM,aAAa,GAAG,GAAG,EAAE;YACzB,IAAI;gBACF,MAAM,OAAO,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;gBAC7C,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAa,EAAE,EAAE,CACxD,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,CACnC,CAAC;gBAEF,KAAK,MAAM,WAAW,IAAI,gBAAgB,EAAE;oBAC1C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;wBAC1C,IAAI;4BACF,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;4BACrE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;4BACtC,KAAK,CAAC,qCAAqB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;yBAC/D;wBAAC,OAAO,KAAU,EAAE;4BACnB,OAAO,CAAC,IAAI,CACV,qCAAqB,CAAC,yBAAyB,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAC5E,CAAC;yBACH;qBACF;iBACF;aACF;YAAC,OAAO,KAAU,EAAE;gBACnB,KAAK,CAAC,qCAAqB,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;aACxE;QACH,CAAC,CAAC;QAEF,IAAI,OAAO,EAAE;YACX,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC9C;aAAM;YACL,aAAa,EAAE,CAAC;SACjB;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,WAAoB,EAAE,QAA2B;QAClF,OAAO,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAkB,CAAC;IACtF,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,uBAAuB,CACpC,eAAuB,EACvB,WAAmB,EACnB,QAA2B;QAE3B,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;QAE5C,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;QAC1D,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAE7D,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,uBAAuB,CAAC,CAAC;SACzE;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,2BAA2B,CAAC,CAAC;SAC7E;QAED,iDAAiD;QACjD,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QAE3C,oCAAoC;QACpC,IAAI,YAA8B,CAAC;QACnC,IAAI,cAAc,CAAC,OAAO,IAAI,OAAO,cAAc,CAAC,OAAO,KAAK,QAAQ,EAAE;YACxE,gCAAgC;YAChC,YAAY,GAAG,cAAc,CAAC,OAA2B,CAAC;SAC3D;aAAM,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc,CAAC,QAAQ,EAAE;YACxE,qCAAqC;YACrC,YAAY,GAAG,cAAkC,CAAC;SACnD;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,sCAAsC,CAAC,CAAC;SACxF;QAED,wDAAwD;QACxD,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SACjC;aAAM;YACL,4DAA4D;YAC5D,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YAChD,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,yBAAyB,CAAC,WAAoB,EAAE,QAA2B;QAChF,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB;QAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,oBAAoB;QACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAC/B,WAAmB,EACnB,WAAoB,EACpB,QAA2B;QAE3B,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,mBAAmB,GAAG,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACzD,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;QACrE,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,4BAA4B,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QACrE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;;AAlKH,8CAmKC;AAlKgB,iCAAe,GAAG,IAAI,GAAG,EAAU,CAAC"}