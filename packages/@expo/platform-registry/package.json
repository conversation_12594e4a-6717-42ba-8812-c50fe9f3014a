{"name": "@expo/platform-registry", "version": "1.0.0", "description": "External platform registry for Expo CLI", "main": "build/index.js", "types": "build/index.d.ts", "license": "MIT", "files": ["build"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "test": "expo-module test", "lint": "expo-module lint", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/config-types": "^52.0.5", "debug": "^4.3.4", "fs-extra": "^11.1.1"}, "devDependencies": {"expo-module-scripts": "*"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/platform-registry"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/platform-registry#readme"}