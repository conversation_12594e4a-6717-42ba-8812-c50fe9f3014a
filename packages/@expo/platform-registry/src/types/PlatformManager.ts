/**
 * External platform prerequisite constructor.
 * Allows external platforms to provide health checking and prerequisite validation.
 */
export type ExternalPlatformPrerequisiteConstructor = new (
  platform: string
) => ExternalPlatformPrerequisite;

/**
 * Abstract base class for external platform prerequisites.
 * External platforms should extend this to provide health checking.
 */
export abstract class ExternalPlatformPrerequisite {
  constructor(protected platform: string) {}

  /**
   * Check if the development environment is properly set up for this platform.
   * @returns Promise resolving to true if environment is ready
   */
  abstract checkDevelopmentEnvironment(): Promise<boolean>;

  /**
   * Check if system requirements are met for this platform.
   * @returns Promise resolving to true if requirements are met
   */
  abstract checkSystemRequirements(): Promise<boolean>;

  /**
   * Check if platform-specific tools are installed and available.
   * @returns Promise resolving to true if tools are available
   */
  abstract checkPlatformTools(): Promise<boolean>;

  /**
   * Get installation instructions for missing prerequisites.
   * @returns Array of instruction strings
   */
  abstract getInstallationInstructions(): string[];

  /**
   * Run all prerequisite checks and throw if any fail.
   * This is the main entry point called by the doctor command.
   */
  async assertAsync(): Promise<void> {
    const checks = [
      { name: 'Development Environment', check: () => this.checkDevelopmentEnvironment() },
      { name: 'System Requirements', check: () => this.checkSystemRequirements() },
      { name: 'Platform Tools', check: () => this.checkPlatformTools() },
    ];

    const failures: string[] = [];

    for (const { name, check } of checks) {
      try {
        const result = await check();
        if (!result) {
          failures.push(name);
        }
      } catch (error: any) {
        failures.push(`${name}: ${error.message}`);
      }
    }

    if (failures.length > 0) {
      const instructions = this.getInstallationInstructions();
      const instructionText =
        instructions.length > 0
          ? `\n\nTo fix these issues:\n${instructions.map((i) => `  • ${i}`).join('\n')}`
          : '';

      throw new Error(
        `Platform "${this.platform}" prerequisites not met:\n` +
          `${failures.map((f) => `  ✗ ${f}`).join('\n')}${instructionText}`
      );
    }
  }
}
