/**
 * Generic run options interface for external platforms.
 * Provides a common interface that external platforms can use for their run commands.
 */
export interface RunOptions {
    /** Target device (device ID, 'device', 'emulator', or boolean for prompting) */
    device?: string | boolean;
    /** Dev server port to use */
    port?: number;
    /** Should start the bundler dev server */
    bundler?: boolean;
    /** Should install missing dependencies before building */
    install?: boolean;
    /** Should use build cache */
    buildCache?: boolean;
    /** Path to an existing binary to install on the device */
    binary?: string;
    /** Build variant/configuration (e.g., 'debug', 'release') */
    variant?: string;
    /** Build configuration (e.g., 'Debug', 'Release') */
    configuration?: string;
    /** Build scheme (platform-specific) */
    scheme?: string | boolean;
    /** Additional platform-specific options */
    [key: string]: any;
}
/**
 * External platform dependency resolver interface.
 * Allows external platforms to provide platform-specific dependency resolution.
 */
export interface ExternalPlatformDependencyResolver {
    /**
     * Resolve platform-specific dependencies for given packages.
     *
     * @param packages List of packages being installed
     * @param sdkVersion Current Expo SDK version
     * @returns Promise resolving to additional packages that should be installed
     */
    resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]>;
    /**
     * Get platform-specific autolinking configuration.
     * This is called after package installation to configure autolinking.
     *
     * @param projectRoot Project root directory
     * @param packages List of installed packages
     * @returns Promise resolving when autolinking is configured
     */
    configureAutolinking?(projectRoot: string, packages: string[]): Promise<void>;
}
/**
 * Autolinking implementation interface for platform-specific native module integration
 */
export interface AutolinkingImplementation {
    resolveModuleAsync(moduleName: string, projectRoot: string): Promise<any>;
    generatePackageListAsync(projectRoot: string): Promise<string>;
    getLinkingConfigAsync(projectRoot: string): Promise<any>;
}
/**
 * SDK module compatibility declaration
 */
export interface SDKModuleCompatibility {
    [moduleName: string]: {
        supported: boolean;
        version?: string;
        implementation?: string;
        fallback?: string;
    };
}
/**
 * Asset handler configuration for platform-specific asset processing
 */
export interface AssetHandlerConfiguration {
    appIcon?: {
        sizes: number[];
        format: string;
        generator?: string;
    };
    splashScreen?: {
        supported: boolean;
        fallback?: string;
        handler?: string;
    };
    fonts?: {
        formats: string[];
        handler?: string;
    };
}
/**
 * Dev client extension configuration for platform-specific development tools
 */
export interface DevClientExtensionConfiguration {
    devMenuItems?: {
        name: string;
        action: string;
        icon?: string;
    }[];
    errorBoundaries?: {
        [errorType: string]: string;
    };
    debugTools?: {
        [toolName: string]: string;
    };
    inspectors?: {
        [inspectorName: string]: string;
    };
}
/**
 * Test utility configuration for platform-specific testing support
 */
export interface TestUtilityConfiguration {
    testEnvironment?: string;
    setupFiles?: string[];
    moduleNameMapper?: {
        [pattern: string]: string;
    };
    platformMocks?: {
        [mockName: string]: any;
    };
    testUtilities?: {
        [utilityName: string]: string;
    };
}
/**
 * Permission configuration for platform-specific permission handling
 */
export interface PlatformPermissionConfiguration {
    [permissionName: string]: {
        key: string;
        description: string;
        required: boolean;
        fallback?: string;
    };
}
/**
 * External platform interface with essential integration points.
 * Enables full out-of-tree platform integration while remaining lean.
 */
export interface ExternalPlatform {
    /** Platform name (e.g., 'macos', 'windows') */
    platform: string;
    /** Display name for the platform (e.g., 'macOS', 'Windows') */
    displayName?: string;
    /** Device manager constructor that extends DeviceManager base class */
    deviceManagerConstructor?: any;
    /** Platform manager constructor that extends Expo's PlatformManager base class */
    platformManagerConstructor?: any;
    /** App ID resolver constructor for this platform */
    appIdResolverConstructor?: any;
    /** Prerequisite constructor for health checking and validation */
    prerequisiteConstructor?: any;
    /** Optional run function for native integration - enables 90-95% run command parity */
    runAsync?: (projectRoot: string, options: RunOptions) => Promise<void>;
    /** Optional dependency resolver for platform-specific package management */
    dependencyResolver?: ExternalPlatformDependencyResolver;
    /** Config plugins to auto-add during prebuild */
    configPlugins?: string[];
    /** Metro file extensions for platform-specific files */
    metroExtensions?: string[];
    /** Template path for expo prebuild */
    templatePath?: string;
    /** Autolinking implementation for native module integration */
    autolinkingImplementation?: AutolinkingImplementation;
    /** SDK module compatibility declarations */
    supportedModules?: SDKModuleCompatibility;
    /** Asset handler configuration for platform-specific assets */
    assetHandlers?: AssetHandlerConfiguration;
    /** Dev client extension configuration for development tools */
    devClientExtensions?: DevClientExtensionConfiguration;
    /** Test utility configuration for platform-specific testing */
    testUtilities?: TestUtilityConfiguration;
    /** Permission configuration for platform-specific permissions */
    permissions?: PlatformPermissionConfiguration;
    /** Advanced debugging configuration for platform-specific debugging tools */
    debuggingConfig?: any;
    /** Advanced Metro configuration for platform-specific bundling */
    metroConfig?: any;
    /** Platform-specific validation rules */
    validationRules?: any[];
    /** Platform-specific customization templates */
    customizationTemplates?: {
        [templateName: string]: {
            source: string;
            destination: string;
            dependencies?: string[];
        };
    };
}
//# sourceMappingURL=ExternalPlatform.d.ts.map