"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getAllAvailablePlatforms = getAllAvailablePlatforms;
exports.isExternalPlatform = isExternalPlatform;
exports.withExternalPlatformBaseMod = withExternalPlatformBaseMod;
exports.withExternalPlatformBaseMods = withExternalPlatformBaseMods;
function _createBaseMod() {
  const data = require("./createBaseMod");
  _createBaseMod = function () {
    return data;
  };
  return data;
}
function _platformRegistry() {
  const data = require("@expo/platform-registry");
  _platformRegistry = function () {
    return data;
  };
  return data;
}
/**
 * External platform mod support for config plugins.
 * This enables external platforms to register their own mods and base mods.
 */

/**
 * Generic external platform mod configuration.
 * External platforms can extend this interface using TypeScript declaration merging.
 */

/**
 * Plugin to add external platform base mods to the config.
 * This automatically discovers and registers base mods for all available external platforms.
 */
function withExternalPlatformBaseMods(config, props = {}) {
  // Load external platforms
  _platformRegistry().PlatformDiscovery.loadExternalPlatformsSync();

  // Get all available external platforms
  const externalPlatforms = _platformRegistry().platformRegistry.getAllPlatforms();

  // Register base mods for each external platform
  for (const platform of externalPlatforms) {
    config = withExternalPlatformBaseMod(config, {
      ...props,
      platform: platform.platform
    });
  }
  return config;
}

/**
 * Plugin to add base mods for a specific external platform.
 */
function withExternalPlatformBaseMod(config, {
  platform,
  ...props
}) {
  // Get platform data
  const platformData = _platformRegistry().platformRegistry.getPlatform(platform);
  if (!platformData) {
    console.warn(`External platform "${platform}" not found, skipping base mods`);
    return config;
  }

  // Create default providers for the external platform
  const providers = createExternalPlatformProviders(platform);

  // Cast platform to work with withGeneratedBaseMods
  return (0, _createBaseMod().withGeneratedBaseMods)(config, {
    ...props,
    platform: platform,
    // Allow external platforms
    providers
  });
}

/**
 * Create default providers for an external platform.
 * These provide basic file access patterns that external platforms can use.
 */
function createExternalPlatformProviders(platform) {
  return {
    dangerous: {
      getFilePath: () => '',
      read: async () => ({}),
      write: async () => {}
    },
    finalized: {
      getFilePath: () => '',
      read: async () => ({}),
      write: async () => {}
    },
    manifest: {
      getFilePath: ({
        platformProjectRoot
      }) => `${platformProjectRoot}/${platform}-manifest.json`,
      read: async filePath => {
        try {
          const fs = require('fs');
          return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch {
          return {};
        }
      },
      write: async (filePath, data) => {
        const fs = require('fs');
        const path = require('path');
        await fs.promises.mkdir(path.dirname(filePath), {
          recursive: true
        });
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
      }
    },
    config: {
      getFilePath: ({
        platformProjectRoot
      }) => `${platformProjectRoot}/${platform}-config.json`,
      read: async filePath => {
        try {
          const fs = require('fs');
          return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch {
          return {};
        }
      },
      write: async (filePath, data) => {
        const fs = require('fs');
        const path = require('path');
        await fs.promises.mkdir(path.dirname(filePath), {
          recursive: true
        });
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
      }
    }
  };
}

/**
 * Helper function to check if a platform is an external platform.
 */
function isExternalPlatform(platform) {
  return _platformRegistry().platformRegistry.hasPlatform(platform);
}

/**
 * Get all available platforms including external ones.
 */
function getAllAvailablePlatforms() {
  return ['ios', 'android', 'web', ..._platformRegistry().platformRegistry.getAvailablePlatforms()];
}
//# sourceMappingURL=withExternalPlatformMods.js.map