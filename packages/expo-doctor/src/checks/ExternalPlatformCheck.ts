/**
 * External Platform Check for expo-doctor
 * 
 * This check validates external platform prerequisites and health.
 * It integrates with external platforms that provide health checking
 * through the ExternalPlatformPrerequisite interface.
 */

import chalk from 'chalk';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './checks.types';
import { Log } from '../utils/log';

/**
 * Check that validates external platform prerequisites and development environment setup.
 * This integrates with external platforms to provide comprehensive health checking.
 */
export class ExternalPlatformCheck implements Doctor<PERSON><PERSON><PERSON> {
  description = 'External platform prerequisites and health';
  sdkVersionRange = '>=46.0.0';

  async runAsync(params: <PERSON><PERSON><PERSON><PERSON>Para<PERSON>): Promise<DoctorCheckResult> {
    const { projectRoot, exp } = params;
    const issues: string[] = [];
    let hasErrors = false;

    try {
      // Get configured platforms from expo config
      const platforms = exp.platforms || ['ios', 'android'];
      const externalPlatforms = platforms.filter(p => !['ios', 'android', 'web'].includes(p));

      if (externalPlatforms.length === 0) {
        // No external platforms configured - this is fine
        return {
          isSuccessful: true,
          issues: [],
        };
      }

      // Dynamically require platform registry to avoid TypeScript issues
      // This is optional - if platform registry is not available, external platforms won't be checked
      let PlatformDiscovery: any;
      let platformRegistry: any;

      try {
        // Try new platform registry package first
        const platformModule = require('@expo/platform-registry');
        PlatformDiscovery = platformModule.PlatformDiscovery;
        platformRegistry = platformModule.platformRegistry;
      } catch (registryError) {
        try {
          // Fallback to CLI package for backward compatibility
          const platformModule = require('@expo/cli/src/core/PlatformRegistry');
          PlatformDiscovery = platformModule.PlatformDiscovery;
          platformRegistry = platformModule.platformRegistry;
        } catch (cliError) {
          // Platform registry not available - external platforms can't be checked
          return {
            isSuccessful: true,
            issues: [],
            advice: 'External platform checking requires @expo/platform-registry or @expo/cli to be available',
          };
        }
      }

      // Load external platforms
      await PlatformDiscovery.loadExternalPlatforms(projectRoot);

      // Check each external platform
      for (const platformName of externalPlatforms) {
        const platformData = platformRegistry.getPlatform(platformName);

        if (!platformData) {
          issues.push(`External platform "${platformName}" is configured but not installed or not found`);
          hasErrors = true;
          continue;
        }

        if (!platformData.prerequisiteConstructor) {
          // Platform doesn't provide health checks - this is okay, just note it
          Log.log(chalk.gray(`  ${platformName}: No health checks available`));
          continue;
        }

        try {
          const prerequisite = new platformData.prerequisiteConstructor(platformName);
          await prerequisite.assertAsync();
          Log.log(chalk.green(`  ${platformName}: All prerequisites met`));
        } catch (error: any) {
          issues.push(`${platformName}: ${error.message}`);
          hasErrors = true;
        }
      }

      return {
        isSuccessful: !hasErrors,
        issues,
        advice: hasErrors 
          ? 'Follow the platform-specific installation instructions shown above to resolve prerequisite issues'
          : undefined,
      };

    } catch (error: any) {
      // If we can't load external platforms at all, that's a more serious issue
      return {
        isSuccessful: false,
        issues: [`Failed to check external platforms: ${error.message}`],
        advice: 'Ensure external platform packages are properly installed and compatible with your Expo SDK version',
      };
    }
  }
}
