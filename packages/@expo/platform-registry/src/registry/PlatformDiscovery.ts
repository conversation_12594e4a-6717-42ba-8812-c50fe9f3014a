import { ExternalPlatform } from '../types/ExternalPlatform';
import { PlatformRegistry } from './PlatformRegistry';
import { PlatformErrorMessages } from './ErrorMessages';

/**
 * Simple platform discovery service that finds expo-platform-* packages.
 * This focuses on auto-discovery and auto-wiring without complex caching or validation.
 */
export class PlatformDiscovery {
  private static loadedPlatforms = new Set<string>();

  /**
   * Core implementation for loading external platform packages.
   * This handles both sync and async loading scenarios.
   */
  private static loadExternalPlatformsCore(
    projectRoot?: string,
    isAsync: boolean = true,
    registry?: PlatformRegistry
  ): Promise<void> | void {
    const { readdirSync, existsSync } = require('fs');
    const { resolve } = require('path');
    const debug = require('debug')('expo:platform-discovery') as typeof console.log;

    const resolvedProjectRoot = projectRoot || process.cwd();
    const nodeModulesPath = resolve(resolvedProjectRoot, 'node_modules');

    if (!existsSync(nodeModulesPath)) {
      debug(PlatformErrorMessages.debugNodeModulesNotFound());
      return isAsync ? Promise.resolve() : undefined;
    }

    const loadPlatforms = () => {
      try {
        const entries = readdirSync(nodeModulesPath);
        const platformPackages = entries.filter((entry: string) =>
          entry.startsWith('expo-platform-')
        );

        for (const packageName of platformPackages) {
          if (!this.loadedPlatforms.has(packageName)) {
            try {
              this.loadPlatformPackageCore(nodeModulesPath, packageName, registry);
              this.loadedPlatforms.add(packageName);
              debug(PlatformErrorMessages.debugPlatformLoaded(packageName));
            } catch (error: any) {
              console.warn(
                PlatformErrorMessages.platformPackageLoadFailed(packageName, error.message)
              );
            }
          }
        }
      } catch (error: any) {
        debug(PlatformErrorMessages.debugNodeModulesReadFailed(error.message));
      }
    };

    if (isAsync) {
      return Promise.resolve().then(loadPlatforms);
    } else {
      loadPlatforms();
    }
  }

  /**
   * Load external platform packages from node_modules.
   * Looks for packages matching 'expo-platform-*' pattern.
   */
  static async loadExternalPlatforms(projectRoot?: string, registry?: PlatformRegistry): Promise<void> {
    return this.loadExternalPlatformsCore(projectRoot, true, registry) as Promise<void>;
  }

  /**
   * Load a specific platform package and register it.
   * This is the core implementation that handles both sync and async loading.
   */
  private static loadPlatformPackageCore(
    nodeModulesPath: string, 
    packageName: string,
    registry?: PlatformRegistry
  ): void {
    const { resolve } = require('path');
    const { existsSync, readFileSync } = require('fs');
    const resolveFrom = require('resolve-from');

    const packagePath = resolve(nodeModulesPath, packageName);
    const packageJsonPath = resolve(packagePath, 'package.json');

    if (!existsSync(packageJsonPath)) {
      throw new Error(`Platform package ${packageName} missing package.json`);
    }

    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

    if (!packageJson.main) {
      throw new Error(`Platform package ${packageName} missing main entry point`);
    }

    // Use resolveFrom to properly resolve the module
    const modulePath = resolveFrom(packagePath, '.');
    const platformModule = require(modulePath);

    // Check if it exports platform data
    let platformData: ExternalPlatform;
    if (platformModule.default && typeof platformModule.default === 'object') {
      // ES module with default export
      platformData = platformModule.default as ExternalPlatform;
    } else if (typeof platformModule === 'object' && platformModule.platform) {
      // CommonJS module with platform data
      platformData = platformModule as ExternalPlatform;
    } else {
      throw new Error(`Platform package ${packageName} does not export valid platform data`);
    }

    // Register with the provided registry or the global one
    if (registry) {
      registry.register(platformData);
    } else {
      // Import the global registry to avoid circular dependencies
      const { platformRegistry } = require('./index');
      platformRegistry.register(platformData);
    }
  }

  /**
   * Load external platform packages synchronously.
   * This is used by Metro configuration which requires synchronous loading.
   * Looks for packages matching 'expo-platform-*' pattern.
   */
  static loadExternalPlatformsSync(projectRoot?: string, registry?: PlatformRegistry): void {
    this.loadExternalPlatformsCore(projectRoot, false, registry);
  }

  /**
   * Get the list of loaded platform package names.
   */
  static getLoadedPlatformPackages(): string[] {
    return Array.from(this.loadedPlatforms);
  }

  /**
   * Clear the loaded platforms cache.
   * This is primarily useful for testing.
   */
  static clearLoadedPlatforms(): void {
    this.loadedPlatforms.clear();
  }

  /**
   * Load a specific platform package by name.
   * This is useful for testing or when you know the exact package name.
   */
  static async loadSpecificPlatform(
    packageName: string, 
    projectRoot?: string,
    registry?: PlatformRegistry
  ): Promise<void> {
    const { resolve } = require('path');
    const { existsSync } = require('fs');

    const resolvedProjectRoot = projectRoot || process.cwd();
    const nodeModulesPath = resolve(resolvedProjectRoot, 'node_modules');
    const packagePath = resolve(nodeModulesPath, packageName);

    if (!existsSync(packagePath)) {
      throw new Error(`Platform package ${packageName} not found in node_modules`);
    }

    this.loadPlatformPackageCore(nodeModulesPath, packageName, registry);
    this.loadedPlatforms.add(packageName);
  }
}
