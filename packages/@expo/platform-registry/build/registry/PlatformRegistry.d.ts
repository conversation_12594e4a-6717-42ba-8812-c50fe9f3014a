import { ExternalPlatform } from '../types/ExternalPlatform';
/**
 * Simple registry for tracking external platforms.
 * Focuses only on essential platform management for Phase 0.
 */
export declare class PlatformRegistry {
    private platforms;
    /**
     * Register an external platform.
     * @param platformData The platform data from expo-platform-* package
     */
    register(platformData: ExternalPlatform): void;
    /**
     * Register development tools for a platform.
     * This is called automatically when a platform is registered.
     * @param platformData The platform data
     */
    private registerDevelopmentTools;
    /**
     * Register advanced features for a platform.
     * This is called automatically when a platform is registered.
     * @param platformData The platform data
     */
    private registerAdvancedFeatures;
    /**
     * Get all available external platform names.
     * @returns Array of platform names
     */
    getAvailablePlatforms(): string[];
    /**
     * Check if a platform is registered.
     * @param platform The platform identifier
     * @returns True if the platform is registered
     */
    hasPlatform(platform: string): boolean;
    /**
     * Get platform data.
     * @param platform The platform identifier
     * @returns Platform data or undefined if not found
     */
    getPlatform(platform: string): ExternalPlatform | undefined;
    /**
     * Get all registered platforms.
     * @returns Array of platform data
     */
    getAllPlatforms(): ExternalPlatform[];
    /**
     * Clear all registered platforms.
     * This is primarily useful for testing.
     */
    clear(): void;
    /**
     * Get platforms that support a specific feature.
     * @param feature The feature to check for
     * @returns Array of platform names that support the feature
     */
    getPlatformsWithFeature(feature: keyof ExternalPlatform): string[];
    /**
     * Get platforms that have customization templates.
     * @returns Array of platform names with customization support
     */
    getPlatformsWithCustomization(): string[];
    /**
     * Get customization templates for a platform.
     * @param platform The platform identifier
     * @returns Customization templates or undefined if not available
     */
    getCustomizationTemplates(platform: string): ExternalPlatform['customizationTemplates'];
}
//# sourceMappingURL=PlatformRegistry.d.ts.map