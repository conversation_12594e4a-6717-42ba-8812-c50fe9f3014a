import { PlatformRegistry } from './PlatformRegistry';
/**
 * Simple platform discovery service that finds expo-platform-* packages.
 * This focuses on auto-discovery and auto-wiring without complex caching or validation.
 */
export declare class PlatformDiscovery {
    private static loadedPlatforms;
    /**
     * Core implementation for loading external platform packages.
     * This handles both sync and async loading scenarios.
     */
    private static loadExternalPlatformsCore;
    /**
     * Load external platform packages from node_modules.
     * Looks for packages matching 'expo-platform-*' pattern.
     */
    static loadExternalPlatforms(projectRoot?: string, registry?: PlatformRegistry): Promise<void>;
    /**
     * Load a specific platform package and register it.
     * This is the core implementation that handles both sync and async loading.
     */
    private static loadPlatformPackageCore;
    /**
     * Load external platform packages synchronously.
     * This is used by Metro configuration which requires synchronous loading.
     * Looks for packages matching 'expo-platform-*' pattern.
     */
    static loadExternalPlatformsSync(projectRoot?: string, registry?: PlatformRegistry): void;
    /**
     * Get the list of loaded platform package names.
     */
    static getLoadedPlatformPackages(): string[];
    /**
     * Clear the loaded platforms cache.
     * This is primarily useful for testing.
     */
    static clearLoadedPlatforms(): void;
    /**
     * Load a specific platform package by name.
     * This is useful for testing or when you know the exact package name.
     */
    static loadSpecificPlatform(packageName: string, projectRoot?: string, registry?: PlatformRegistry): Promise<void>;
}
//# sourceMappingURL=PlatformDiscovery.d.ts.map