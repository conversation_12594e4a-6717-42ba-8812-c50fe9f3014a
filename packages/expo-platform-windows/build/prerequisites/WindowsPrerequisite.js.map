{"version": 3, "file": "WindowsPrerequisite.js", "sourceRoot": "", "sources": ["../../src/prerequisites/WindowsPrerequisite.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,0EAAmF;AACnF,iDAAyC;AACzC,uCAAyB;AACzB,2CAA6B;AAE7B;;;;GAIG;AACH,MAAa,mBAAoB,SAAQ,+CAA4B;IACnE,YAAY,QAAgB;QAC1B,KAAK,CAAC,QAAQ,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B;QAC/B,IAAI;YACF,4BAA4B;YAC5B,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,gEAAgE;YAChE,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;YACpC,IAAI,CAAC,WAAW,EAAE;gBAChB,OAAO,KAAK,CAAC;aACd;YAED,sCAAsC;YACtC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,YAAY,GAAG,EAAE,EAAE;gBACrB,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB;QAC3B,IAAI;YACF,wBAAwB;YACxB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACtD,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE;gBACtE,OAAO,KAAK,CAAC;aACd;YAED,uBAAuB;YACvB,IAAI;gBACF,IAAA,wBAAQ,EAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;aAC/D;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI;YACF,yCAAyC;YACzC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvD,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO,KAAK,CAAC;aACd;YAED,wBAAwB;YACxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO,KAAK,CAAC;aACd;YAED,oBAAoB;YACpB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAChC,YAAY,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACpE,OAAO,YAAY,CAAC;SACrB;QAED,YAAY,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QACtF,YAAY,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACtD,YAAY,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAChE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,YAAY,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC1E,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC5D,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,YAAY,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QAClF,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,YAAY,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC1D,YAAY,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACjE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,YAAY,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC7D,YAAY,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QAE9F,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,IAAI;YACF,MAAM,MAAM,GAAG,IAAA,wBAAQ,EAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACpE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;SACtB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,OAAe;QAC/C,0DAA0D;QAC1D,oCAAoC;QACpC,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,gCAAgC;QAChC,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE;YAC/B,OAAO,KAAK,IAAI,KAAK,CAAC,CAAC,0BAA0B;SAClD;QAED,iCAAiC;QACjC,IAAI,KAAK,IAAI,EAAE,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,IAAI;YACF,wCAAwC;YACxC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAC3B,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,yBAAyB,EAC7D,yBAAyB,EACzB,WAAW,EACX,aAAa,CACd,CAAC;YAEF,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC9B,MAAM,MAAM,GAAG,IAAA,wBAAQ,EAAC,IAAI,WAAW,mFAAmF,EAAE;oBAC1H,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,MAAM;iBACd,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;aACjC;YAED,iDAAiD;YACjD,MAAM,WAAW,GAAG;gBAClB,kDAAkD;gBAClD,wDAAwD;gBACxD,kDAAkD;aACnD,CAAC;YAEF,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;SAC1D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI;YACF,MAAM,OAAO,GAAG,gDAAgD,CAAC;YACjE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACd;YAED,qCAAqC;YACrC,MAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CACpD,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CACzE,CAAC;YAEF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;SAC5B;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI;YACF,IAAA,wBAAQ,EAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,wBAAwB;YACxB,MAAM,YAAY,GAAG;gBACnB,kGAAkG;gBAClG,oGAAoG;gBACpG,iGAAiG;gBACjG,wGAAwG;gBACxG,0GAA0G;gBAC1G,uGAAuG;aACxG,CAAC;YAEF,OAAO,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;SACrE;IACH,CAAC;CACF;AAxOD,kDAwOC"}