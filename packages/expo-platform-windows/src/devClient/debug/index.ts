/**
 * Windows Debug Tools Configuration
 *
 * Provides configuration for Windows-specific debug and inspection tools.
 * The actual UI components would be implemented by the Expo development client.
 */

export interface WindowsDebugToolConfig {
  name: string;
  description: string;
  category: 'performance' | 'debugging' | 'inspection' | 'profiling';
  component?: string;
  handler?: string;
  icon?: string;
  shortcut?: string;
}

/**
 * Windows Inspector Configuration
 */
export const windowsInspectorConfig: WindowsDebugToolConfig = {
  name: 'Windows Inspector',
  description: 'Inspect Windows-specific properties, capabilities, and system information',
  category: 'inspection',
  component: 'expo-platform-windows/devClient/components/WindowsInspector',
  handler: 'expo-platform-windows/devClient/handlers/windowsInspectorHandler',
  icon: 'search',
  shortcut: 'Ctrl+Shift+I',
};

/**
 * Memory Profiler Configuration
 */
export const memoryProfilerConfig: WindowsDebugToolConfig = {
  name: 'Memory Profiler',
  description: 'Monitor Windows memory usage and performance metrics',
  category: 'profiling',
  component: 'expo-platform-windows/devClient/components/MemoryProfiler',
  handler: 'expo-platform-windows/devClient/handlers/memoryProfilerHandler',
  icon: 'memory',
  shortcut: 'Ctrl+Shift+M',
};

/**
 * UWP Capability Inspector Configuration
 */
export const uwpCapabilityInspectorConfig: WindowsDebugToolConfig = {
  name: 'UWP Capability Inspector',
  description: 'View and test UWP capabilities and permissions',
  category: 'inspection',
  component: 'expo-platform-windows/devClient/components/UWPCapabilityInspector',
  handler: 'expo-platform-windows/devClient/handlers/uwpCapabilityInspectorHandler',
  icon: 'shield',
  shortcut: 'Ctrl+Shift+U',
};

/**
 * Performance Counter Configuration
 */
export const performanceCounterConfig: WindowsDebugToolConfig = {
  name: 'Windows Performance Counter',
  description: 'Monitor Windows performance counters and system metrics',
  category: 'performance',
  component: 'expo-platform-windows/devClient/components/PerformanceCounter',
  handler: 'expo-platform-windows/devClient/handlers/performanceCounterHandler',
  icon: 'chart',
  shortcut: 'Ctrl+Shift+P',
};

/**
 * Get all Windows debug tool configurations
 */
export function getWindowsDebugToolConfigs(): WindowsDebugToolConfig[] {
  return [
    windowsInspectorConfig,
    memoryProfilerConfig,
    uwpCapabilityInspectorConfig,
    performanceCounterConfig,
  ];
}

/**
 * Get debug tool configuration by name
 */
export function getWindowsDebugToolConfig(name: string): WindowsDebugToolConfig | undefined {
  return getWindowsDebugToolConfigs().find(tool => tool.name === name);
}
