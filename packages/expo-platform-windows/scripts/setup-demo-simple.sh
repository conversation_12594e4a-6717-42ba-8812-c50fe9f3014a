#!/bin/bash

# Simple Demo Setup Script for Expo Platform Windows
# Focuses on getting local dependencies working correctly

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Configuration
EXPO_ROOT="/Users/<USER>/Code/thirdparty/expo"
DEMO_DIR="$HOME/expo-windows-demos"

echo "🚀 Simple Expo Platform Windows Demo Setup"
echo "==========================================="

# Step 1: Build Expo packages
log_info "Step 1: Building Expo packages..."
cd "$EXPO_ROOT"

if [ ! -f "packages/expo-platform-windows/build/index.js" ]; then
    log_info "Building expo-platform-windows..."
    cd packages/expo-platform-windows
    yarn build
    cd "$EXPO_ROOT"
fi

if [ ! -f "packages/@expo/cli/build/bin/cli" ]; then
    log_info "Building Expo CLI..."
    cd packages/@expo/cli
    yarn build
    cd "$EXPO_ROOT"
fi

log_success "Expo packages built"

# Step 2: Create demo directory
log_info "Step 2: Setting up demo directory..."
mkdir -p "$DEMO_DIR"
cd "$DEMO_DIR"

# Step 3: Create blank demo project
log_info "Step 3: Creating blank demo project..."

if [ -d "WindowsBlankDemo" ]; then
    log_warning "WindowsBlankDemo exists, removing..."
    rm -rf WindowsBlankDemo
fi

# Create project using npx create-expo-app
npx create-expo-app@latest WindowsBlankDemo --template blank-typescript --no-install

cd WindowsBlankDemo

# Step 4: Setup local dependencies
log_info "Step 4: Setting up local dependencies..."

# Install base dependencies
npm install

# Add expo-platform-windows as file dependency
log_info "Adding expo-platform-windows as file dependency..."
npm install "file:$EXPO_ROOT/packages/expo-platform-windows"

# Step 5: Configure for Windows
log_info "Step 5: Configuring for Windows platform..."

cat > app.config.js << 'EOF'
export default {
  name: "WindowsBlankDemo",
  slug: "windows-blank-demo",
  version: "1.0.0",
  platforms: ["ios", "android", "windows"],
  orientation: "portrait",
  icon: "./assets/icon.png",
  userInterfaceStyle: "light",
  splash: {
    image: "./assets/splash.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  assetBundlePatterns: ["**/*"],
  plugins: [
    [
      "expo-platform-windows/configPlugins/withWindowsManifest",
      {
        displayName: "Windows Blank Demo",
        publisher: "CN=ExpoDemo",
        backgroundColor: "#ffffff"
      }
    ],
    [
      "expo-platform-windows/configPlugins/withWindowsAssets",
      {
        appIcon: {
          path: "./assets/icon.png",
          sizes: [16, 32, 48, 64, 128, 256]
        }
      }
    ]
  ]
};
EOF

# Step 6: Create test script
log_info "Step 6: Creating test script..."

cat > test-modules.js << 'EOF'
// Test core modules
const testModules = [
  'expo-constants',
  'expo-file-system',
  'expo-linking',
  'expo-status-bar'
];

async function testModule(moduleName) {
  try {
    const module = require(moduleName);
    console.log(`✅ ${moduleName}: Loaded successfully`);
    return true;
  } catch (error) {
    console.log(`❌ ${moduleName}: Failed - ${error.message}`);
    return false;
  }
}

async function testAllModules() {
  console.log('Testing SDK modules...\n');
  
  let passed = 0;
  for (const moduleName of testModules) {
    const success = await testModule(moduleName);
    if (success) passed++;
  }
  
  console.log(`\nResults: ${passed}/${testModules.length} modules loaded`);
}

testAllModules();
EOF

# Step 7: Create CLI alias
log_info "Step 7: Setting up CLI alias..."

SHELL_RC=""
if [ -f "$HOME/.zshrc" ]; then
    SHELL_RC="$HOME/.zshrc"
elif [ -f "$HOME/.bashrc" ]; then
    SHELL_RC="$HOME/.bashrc"
fi

if [ -n "$SHELL_RC" ]; then
    ALIAS_LINE="alias nexpo=\"$EXPO_ROOT/packages/@expo/cli/build/bin/cli\""
    
    if ! grep -q "alias nexpo=" "$SHELL_RC"; then
        echo "" >> "$SHELL_RC"
        echo "# Expo Platform Windows Demo" >> "$SHELL_RC"
        echo "$ALIAS_LINE" >> "$SHELL_RC"
        log_success "CLI alias added to $SHELL_RC"
    else
        log_success "CLI alias already exists"
    fi
fi

# Step 8: Test setup
log_info "Step 8: Testing setup..."

# Test that expo-platform-windows can be required
if node -e "require('expo-platform-windows')" 2>/dev/null; then
    log_success "expo-platform-windows loads correctly"
else
    log_error "expo-platform-windows failed to load"
    exit 1
fi

# Test local CLI
if "$EXPO_ROOT/packages/@expo/cli/build/bin/cli" --version >/dev/null 2>&1; then
    log_success "Local Expo CLI works"
else
    log_error "Local Expo CLI failed"
    exit 1
fi

log_success "Setup complete!"

echo ""
echo "🎉 Next steps:"
echo "=============="
echo ""
echo "1. Reload your shell:"
echo "   source ~/.zshrc  # or ~/.bashrc"
echo ""
echo "2. Navigate to demo project:"
echo "   cd $DEMO_DIR/WindowsBlankDemo"
echo ""
echo "3. Test module loading:"
echo "   node test-modules.js"
echo ""
echo "4. Test platform discovery:"
echo "   nexpo config --type public"
echo ""
echo "5. Test prebuild:"
echo "   nexpo prebuild --platform windows --clear"
echo ""
echo "6. Start Metro:"
echo "   nexpo start --clear-cache"
echo ""
echo "📁 Demo project: $DEMO_DIR/WindowsBlankDemo"
echo "🔧 Local CLI: nexpo (alias for local Expo CLI)"
echo ""
