{"version": 3, "file": "withWindowsPermissions.js", "sourceRoot": "", "sources": ["../../src/configPlugins/withWindowsPermissions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,yDAAsE;AACtE,qDAAwD;AACxD,uCAAyB;AACzB,2CAA6B;AAuB7B;;GAEG;AACI,MAAM,sBAAsB,GAA2C,CAC5E,MAAM,EACN,iBAAiB,GAAG,EAAE,EACtB,EAAE;;IACF,+DAA+D;IAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;QACjB,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;KACnB;IAED,2CAA2C;IAC3C,MAAM,mBAAmB,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAE/C,sDAAsD;IACtD,MAAM,iBAAiB,GAA6B;QAClD,MAAM,EAAE,CAAC,QAAQ,CAAC;QAClB,UAAU,EAAE,CAAC,YAAY,CAAC;QAC1B,QAAQ,EAAE,CAAC,UAAU,CAAC;QACtB,aAAa,EAAE,CAAC,0BAA0B,CAAC;QAC3C,QAAQ,EAAE,CAAC,UAAU,CAAC;QACtB,QAAQ,EAAE,CAAC,cAAc,CAAC;QAC1B,MAAM,EAAE,CAAC,iBAAiB,CAAC;QAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;QACzB,KAAK,EAAE,CAAC,cAAc,CAAC;QACvB,SAAS,EAAE,CAAC,kBAAkB,CAAC;QAC/B,SAAS,EAAE,CAAC,WAAW,CAAC;QACxB,IAAI,EAAE,CAAC,aAAa,CAAC;KACtB,CAAC;IAEF,6CAA6C;IAC7C,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,MAAM,WAAW,GAAI,MAAc,CAAC,WAAW,CAAC;IAChD,IAAI,WAAW,EAAE;QACf,KAAK,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC/D,IAAI,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE;gBAC5C,gBAAgB,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;aACzD;SACF;KACF;IAED,2BAA2B;IAC3B,MAAM,eAAe,GAAG;QACtB,GAAG,mBAAmB;QACtB,GAAG,gBAAgB;QACnB,GAAG,CAAC,iBAAiB,CAAC,YAAY,IAAI,EAAE,CAAC;KAC1C,CAAC;IAEF,oBAAoB;IACpB,MAAM,kBAAkB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;IAEzD,wEAAwE;IACxE,MAAM,CAAC,KAAK,CAAC,kBAAkB,GAAG;QAChC,YAAY,EAAE,kBAAkB;QAChC,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;QAC9D,sBAAsB,EAAE,iBAAiB,CAAC,sBAAsB,IAAI,EAAE;QACtE,kBAAkB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;QAC9D,aAAa,EAAE;YACb,GAAG,EAAE,MAAA,iBAAiB,CAAC,aAAa,0CAAE,GAAG;YACzC,OAAO,EAAE,MAAA,iBAAiB,CAAC,aAAa,0CAAE,OAAO;SAClD;QACD,4CAA4C;QAC5C,wBAAwB,EAAE,gBAAgB;QAC1C,iBAAiB;KAClB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjE,IAAI,MAAA,iBAAiB,CAAC,kBAAkB,0CAAE,MAAM,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC,2BAA2B,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3F;IAED,yDAAyD;IACzD,OAAO,IAAA,iCAAgB,EAAC,MAAM,EAAE;QAC9B,SAAS;QACT,KAAK,EAAE,MAAM,EAAE,EAAE;;YACf,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAErD,qCAAqC;YACrC,MAAM,YAAY,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,EAAE;gBACjB,OAAO,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;gBAC/F,OAAO,MAAM,CAAC;aACf;YAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAE5D,8BAA8B;YAC9B,MAAM,eAAe,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,IAAI,2BAAS,CAAC;gBAC3B,gBAAgB,EAAE,KAAK;gBACvB,mBAAmB,EAAE,IAAI;gBACzB,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAE/C,kCAAkC;YAClC,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CAAC,QAAQ,EAAE,MAAA,MAAM,CAAC,KAAK,0CAAE,kBAAkB,CAAC,CAAC;YAEpG,mCAAmC;YACnC,MAAM,OAAO,GAAG,IAAI,4BAAU,CAAC;gBAC7B,gBAAgB,EAAE,KAAK;gBACvB,mBAAmB,EAAE,IAAI;gBACzB,YAAY,EAAE,OAAO;gBACrB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACxD,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YAEhD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AAlHW,QAAA,sBAAsB,0BAkHjC;AAEF;;GAEG;AACH,SAAS,mBAAmB,CAAC,UAAkB;IAC7C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,IAAI,CAAC;KACb;IAED,oDAAoD;IACpD,MAAM,aAAa,GAAG;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE,sBAAsB,CAAC;KAChE,CAAC;IAEF,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;QACxC,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;YAC/B,OAAO,YAAY,CAAC;SACrB;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,wBAAwB,CAAC,QAAa,EAAE,iBAAuB;IAC5E,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,QAAQ,CAAC;KACjB;IAED,0BAA0B;IAC1B,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC;IACxC,IAAI,CAAC,cAAc,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;KACxE;IAED,sDAAsD;IACtD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;QAChC,cAAc,CAAC,YAAY,GAAG,EAAE,CAAC;KAClC;IAED,4BAA4B;IAC5B,IAAI,iBAAiB,CAAC,YAAY,IAAI,iBAAiB,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/E,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC;YACxE,QAAQ,EAAE,GAAG;SACd,CAAC,CAAC,CAAC;QAEJ,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;YACzD,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;SAC9D;aAAM,IAAI,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE;YACjD,cAAc,CAAC,YAAY,CAAC,UAAU,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,YAAY,CAAC,CAAC;SACpG;aAAM;YACL,cAAc,CAAC,YAAY,CAAC,UAAU,GAAG,YAAY,CAAC;SACvD;KACF;IAED,0BAA0B;IAC1B,IAAI,iBAAiB,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3F,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC;YACpF,QAAQ,EAAE,GAAG;SACd,CAAC,CAAC,CAAC;QAEJ,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE;YAC/D,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;SAC1E;aAAM,IAAI,cAAc,CAAC,YAAY,CAAC,gBAAgB,EAAE;YACvD,cAAc,CAAC,YAAY,CAAC,gBAAgB,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,EAAE,GAAG,kBAAkB,CAAC,CAAC;SACtH;aAAM;YACL,cAAc,CAAC,YAAY,CAAC,gBAAgB,GAAG,kBAAkB,CAAC;SACnE;KACF;IAED,8BAA8B;IAC9B,IAAI,iBAAiB,CAAC,sBAAsB,IAAI,iBAAiB,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;QACnG,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC;YAC5F,QAAQ,EAAE,GAAG;SACd,CAAC,CAAC,CAAC;QAEJ,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;YAChE,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,sBAAsB,CAAC,CAAC;SAC/E;aAAM,IAAI,cAAc,CAAC,YAAY,CAAC,iBAAiB,EAAE;YACxD,cAAc,CAAC,YAAY,CAAC,iBAAiB,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,iBAAiB,EAAE,GAAG,sBAAsB,CAAC,CAAC;SAC5H;aAAM;YACL,cAAc,CAAC,YAAY,CAAC,iBAAiB,GAAG,sBAAsB,CAAC;SACxE;KACF;IAED,0BAA0B;IAC1B,IAAI,iBAAiB,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3F,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YACjF,QAAQ,EAAE,GAAG,CAAC,IAAI;YAClB,aAAa,EAAE,GAAG,CAAC,SAAS,IAAI,kFAAkF;SACnH,CAAC,CAAC,CAAC;QAEJ,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,qBAAqB,CAAC,EAAE;YACpE,cAAc,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;SAC/E;aAAM,IAAI,cAAc,CAAC,YAAY,CAAC,qBAAqB,EAAE;YAC5D,cAAc,CAAC,YAAY,CAAC,qBAAqB,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,qBAAqB,EAAE,GAAG,kBAAkB,CAAC,CAAC;SAChI;aAAM;YACL,cAAc,CAAC,YAAY,CAAC,qBAAqB,GAAG,kBAAkB,CAAC;SACxE;KACF;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC"}