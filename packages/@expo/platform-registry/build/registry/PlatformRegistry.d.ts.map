{"version": 3, "file": "PlatformRegistry.d.ts", "sourceRoot": "", "sources": ["../../src/registry/PlatformRegistry.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D;;;GAGG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,SAAS,CAAuC;IAExD;;;OAGG;IACH,QAAQ,CAAC,YAAY,EAAE,gBAAgB,GAAG,IAAI;IAU9C;;;;OAIG;IACH,OAAO,CAAC,wBAAwB;IA+BhC;;;;OAIG;IACH,OAAO,CAAC,wBAAwB;IAchC;;;OAGG;IACH,qBAAqB,IAAI,MAAM,EAAE;IAIjC;;;;OAIG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAItC;;;;OAIG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,gBAAgB,GAAG,SAAS;IAI3D;;;OAGG;IACH,eAAe,IAAI,gBAAgB,EAAE;IAIrC;;;OAGG;IACH,KAAK,IAAI,IAAI;IAIb;;;;OAIG;IACH,uBAAuB,CAAC,OAAO,EAAE,MAAM,gBAAgB,GAAG,MAAM,EAAE;IAMlE;;;OAGG;IACH,6BAA6B,IAAI,MAAM,EAAE;IAIzC;;;;OAIG;IACH,yBAAyB,CAAC,QAAQ,EAAE,MAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC;CAIxF"}