"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.platformRegistry = exports.PlatformErrorMessages = exports.PlatformDiscovery = exports.PlatformRegistry = void 0;
const PlatformRegistry_1 = require("./PlatformRegistry");
// Export all registry classes and utilities
var PlatformRegistry_2 = require("./PlatformRegistry");
Object.defineProperty(exports, "PlatformRegistry", { enumerable: true, get: function () { return PlatformRegistry_2.PlatformRegistry; } });
var PlatformDiscovery_1 = require("./PlatformDiscovery");
Object.defineProperty(exports, "PlatformDiscovery", { enumerable: true, get: function () { return PlatformDiscovery_1.PlatformDiscovery; } });
var ErrorMessages_1 = require("./ErrorMessages");
Object.defineProperty(exports, "PlatformErrorMessages", { enumerable: true, get: function () { return ErrorMessages_1.PlatformErrorMessages; } });
/**
 * Global platform registry instance.
 * This is the main registry used throughout the CLI and other packages.
 */
exports.platformRegistry = new PlatformRegistry_1.PlatformRegistry();
//# sourceMappingURL=index.js.map