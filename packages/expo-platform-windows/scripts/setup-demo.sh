#!/bin/bash

# Expo Platform Windows - Demo Setup Script
# Automates the setup of demo projects for testing

set -e

echo "🚀 Expo Platform Windows - Demo Setup Script"
echo "=============================================="

# Configuration
EXPO_ROOT="/Users/<USER>/Code/thirdparty/expo"
DEMO_DIR="$HOME/expo-windows-demos"
PLATFORM_PACKAGE="$EXPO_ROOT/packages/expo-platform-windows"
CLI_PACKAGE="$EXPO_ROOT/packages/@expo/cli"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    log_success "Node.js found: $(node --version)"
    
    # Check if we're on the right directory
    if [ ! -d "$EXPO_ROOT" ]; then
        log_error "Expo monorepo not found at $EXPO_ROOT"
        exit 1
    fi
    log_success "Expo monorepo found"
    
    # Check if expo-platform-windows exists
    if [ ! -d "$PLATFORM_PACKAGE" ]; then
        log_error "expo-platform-windows package not found"
        exit 1
    fi
    log_success "expo-platform-windows package found"
}

# Build local packages
build_packages() {
    log_info "Building local Expo packages..."

    cd "$EXPO_ROOT"

    # Install dependencies first
    log_info "Installing Expo monorepo dependencies..."
    yarn install

    # Build all packages (this handles workspace dependencies correctly)
    log_info "Building all Expo packages (this may take 10-15 minutes)..."
    yarn build

    # Verify expo-platform-windows built correctly
    log_info "Verifying expo-platform-windows build..."
    if [ ! -f "$PLATFORM_PACKAGE/build/index.js" ]; then
        log_error "expo-platform-windows build failed - build/index.js not found"
        exit 1
    fi
    log_success "expo-platform-windows built successfully"

    # Verify Expo CLI built correctly
    log_info "Verifying Expo CLI build..."
    if [ ! -f "$CLI_PACKAGE/build/bin/cli" ]; then
        log_error "Expo CLI build failed - build/bin/cli not found"
        exit 1
    fi
    log_success "Expo CLI built successfully"
}

# Setup CLI alias
setup_cli_alias() {
    log_info "Setting up CLI alias..."
    
    SHELL_RC=""
    if [ -f "$HOME/.zshrc" ]; then
        SHELL_RC="$HOME/.zshrc"
    elif [ -f "$HOME/.bashrc" ]; then
        SHELL_RC="$HOME/.bashrc"
    else
        log_warning "No shell RC file found, you'll need to set up the alias manually"
        return
    fi
    
    ALIAS_LINE="alias nexpo=\"$CLI_PACKAGE/build/bin/cli\""
    
    if ! grep -q "alias nexpo=" "$SHELL_RC"; then
        echo "" >> "$SHELL_RC"
        echo "# Expo Platform Windows Demo Alias" >> "$SHELL_RC"
        echo "$ALIAS_LINE" >> "$SHELL_RC"
        log_success "CLI alias added to $SHELL_RC"
        log_warning "Please run 'source $SHELL_RC' or restart your terminal"
    else
        log_success "CLI alias already exists"
    fi
}

# Create demo directory
setup_demo_directory() {
    log_info "Setting up demo directory..."
    
    if [ ! -d "$DEMO_DIR" ]; then
        mkdir -p "$DEMO_DIR"
        log_success "Created demo directory: $DEMO_DIR"
    else
        log_success "Demo directory already exists: $DEMO_DIR"
    fi
}

# Setup workspace dependencies for demo projects
setup_workspace_dependencies() {
    log_info "Setting up workspace dependencies..."

    # Create a temporary package.json that references workspace packages
    cd "$DEMO_DIR"

    cat > package.json << EOF
{
  "name": "expo-windows-demos",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "WindowsBlankDemo",
    "WindowsDefaultDemo"
  ],
  "dependencies": {
    "expo-platform-windows": "file:$PLATFORM_PACKAGE"
  }
}
EOF

    log_success "Workspace dependencies configured"
}

# Create blank template project
create_blank_demo() {
    log_info "Creating blank template demo..."
    
    cd "$DEMO_DIR"
    
    if [ -d "WindowsBlankDemo" ]; then
        log_warning "WindowsBlankDemo already exists, skipping creation"
        return
    fi
    
    # Use the local CLI to create project
    "$CLI_PACKAGE/build/bin/cli" create-expo-app WindowsBlankDemo --template blank-typescript --no-install

    cd WindowsBlankDemo

    # Install dependencies and link to local expo-platform-windows
    npm install

    # Add expo-platform-windows as a file dependency
    npm install "file:$PLATFORM_PACKAGE"
    
    # Create app.config.js
    cat > app.config.js << 'EOF'
export default {
  name: "WindowsBlankDemo",
  slug: "windows-blank-demo", 
  version: "1.0.0",
  platforms: ["ios", "android", "windows"],
  orientation: "portrait",
  icon: "./assets/icon.png",
  userInterfaceStyle: "light",
  splash: {
    image: "./assets/splash.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  assetBundlePatterns: ["**/*"],
  plugins: [
    [
      "expo-platform-windows/configPlugins/withWindowsManifest",
      {
        displayName: "Windows Blank Demo",
        publisher: "CN=ExpoDemo",
        backgroundColor: "#ffffff"
      }
    ],
    [
      "expo-platform-windows/configPlugins/withWindowsAssets", 
      {
        appIcon: {
          path: "./assets/icon.png",
          sizes: [16, 32, 48, 64, 128, 256]
        }
      }
    ]
  ]
};
EOF
    
    log_success "Blank demo project created and configured"
}

# Create test script
create_test_script() {
    log_info "Creating module test script..."
    
    cd "$DEMO_DIR/WindowsBlankDemo"
    
    cat > test-modules.js << 'EOF'
// Test core modules that should work
const testModules = [
  'expo-constants',
  'expo-file-system', 
  'expo-linking',
  'expo-web-browser',
  'expo-clipboard',
  'expo-device',
  'expo-font',
  'expo-linear-gradient',
  'expo-status-bar'
];

async function testModule(moduleName) {
  try {
    const module = require(moduleName);
    console.log(`✅ ${moduleName}: Loaded successfully`);
    return true;
  } catch (error) {
    console.log(`❌ ${moduleName}: Failed to load - ${error.message}`);
    return false;
  }
}

async function testAllModules() {
  console.log('Testing SDK modules...\n');
  
  let passed = 0;
  for (const moduleName of testModules) {
    const success = await testModule(moduleName);
    if (success) passed++;
  }
  
  console.log(`\nResults: ${passed}/${testModules.length} modules loaded successfully`);
}

testAllModules();
EOF
    
    log_success "Module test script created"
}

# Print next steps
print_next_steps() {
    echo ""
    echo "🎉 Setup complete! Next steps:"
    echo "=============================="
    echo ""
    echo "1. Reload your shell or run:"
    echo "   source ~/.zshrc  # or ~/.bashrc"
    echo ""
    echo "2. Test the CLI alias:"
    echo "   nexpo --version"
    echo ""
    echo "3. Navigate to the demo project:"
    echo "   cd $DEMO_DIR/WindowsBlankDemo"
    echo ""
    echo "4. Test platform discovery:"
    echo "   nexpo config --type public"
    echo ""
    echo "5. Test prebuild:"
    echo "   nexpo prebuild --platform windows --clear"
    echo ""
    echo "6. Test module loading:"
    echo "   node test-modules.js"
    echo ""
    echo "7. Start Metro:"
    echo "   nexpo start --clear-cache"
    echo ""
    echo "📁 Demo projects location: $DEMO_DIR"
    echo "📋 Follow the full testing plan in DEMO_TESTING_PLAN.md"
}

# Main execution
main() {
    check_prerequisites
    build_packages
    setup_cli_alias
    setup_demo_directory
    setup_workspace_dependencies
    create_blank_demo
    create_test_script
    print_next_steps
}

# Run main function
main "$@"
