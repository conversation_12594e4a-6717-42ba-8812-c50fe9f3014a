"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformErrorMessages = void 0;
/**
 * Centralized error messages for external platform integration.
 * This helps maintain consistency across all platform-related error messages.
 */
class PlatformErrorMessages {
    static platformPackageLoadFailed(packageName, error) {
        const platformName = packageName.replace('expo-platform-', '');
        return (`⚠️  Failed to load external platform package "${packageName}".\n` +
            `   This package will be ignored. Error: ${error}\n\n` +
            `   Common solutions:\n` +
            `   • Reinstall the package: npm install ${packageName}\n` +
            `   • Check package compatibility with your Expo SDK version\n` +
            `   • Verify the package exports valid platform data\n` +
            `   • Check the package documentation for setup requirements\n\n` +
            `   The ${platformName} platform will not be available until this is resolved.`);
    }
    static debugPlatformLoaded(packageName) {
        return `Successfully loaded platform package: ${packageName}`;
    }
    static debugNodeModulesNotFound() {
        return 'node_modules directory not found, skipping platform discovery';
    }
    static debugNodeModulesReadFailed(error) {
        return `Failed to read node_modules directory: ${error}`;
    }
    static platformValidationFailed(platform, availablePlatforms) {
        return (`Platform "${platform}" is not supported.\n\n` +
            `Available platforms: ${availablePlatforms.join(', ')}\n\n` +
            `If "${platform}" is an external platform, install it first:\n` +
            `  npm install expo-platform-${platform}`);
    }
    static noPlatformsConfigured() {
        return ('No platforms are configured for customization.\n\n' +
            'Make sure your app.json includes platforms that support Metro bundling.');
    }
    static noExportDirectoriesFound(searchPath) {
        return (`No export directories found. Run \`npx expo export\` first.\n\n` +
            `Looked for platform-specific directories in: ${searchPath}`);
    }
}
exports.PlatformErrorMessages = PlatformErrorMessages;
//# sourceMappingURL=ErrorMessages.js.map