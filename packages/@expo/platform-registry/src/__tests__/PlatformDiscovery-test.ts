import { PlatformDiscovery } from '../registry/PlatformDiscovery';
import { PlatformRegistry } from '../registry/PlatformRegistry';
import { ExternalPlatform } from '../types/ExternalPlatform';

// Mock fs and path modules
jest.mock('fs', () => ({
  readdirSync: jest.fn(),
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
}));

jest.mock('path', () => ({
  resolve: jest.fn(),
}));

jest.mock('resolve-from', () => jest.fn());

const mockFs = require('fs');
const mockPath = require('path');
const mockResolveFrom = require('resolve-from');

describe('PlatformDiscovery', () => {
  let registry: PlatformRegistry;

  beforeEach(() => {
    registry = new PlatformRegistry();
    PlatformDiscovery.clearLoadedPlatforms();
    jest.clearAllMocks();
  });

  afterEach(() => {
    registry.clear();
    PlatformDiscovery.clearLoadedPlatforms();
  });

  const mockPlatformPackage = (platform: string): ExternalPlatform => ({
    platform,
    displayName: `${platform.charAt(0).toUpperCase() + platform.slice(1)} Platform`,
    metroExtensions: [`.${platform}.js`],
  });

  describe('loadExternalPlatforms', () => {
    it('should handle missing node_modules directory', async () => {
      mockPath.resolve.mockReturnValue('/project/node_modules');
      mockFs.existsSync.mockReturnValue(false);

      await PlatformDiscovery.loadExternalPlatforms('/project', registry);

      expect(registry.getAvailablePlatforms()).toHaveLength(0);
    });

    it('should discover and load expo-platform-* packages', async () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';
      const windowsPackagePath = '/project/node_modules/expo-platform-windows';

      mockPath.resolve
        .mockReturnValueOnce(nodeModulesPath)
        .mockReturnValueOnce(windowsPackagePath)
        .mockReturnValueOnce('/project/node_modules/expo-platform-windows/package.json');

      mockFs.existsSync
        .mockReturnValueOnce(true) // node_modules exists
        .mockReturnValueOnce(true); // package.json exists

      mockFs.readdirSync.mockReturnValue([
        'expo-platform-windows',
        'expo-platform-macos',
        'react-native',
        'expo',
      ]);

      mockFs.readFileSync.mockReturnValue(
        JSON.stringify({
          name: 'expo-platform-windows',
          main: 'build/index.js',
        })
      );

      mockResolveFrom.mockReturnValue('/project/node_modules/expo-platform-windows/build/index.js');

      // Mock the platform module
      const mockPlatformData = mockPlatformPackage('windows');
      jest.doMock('/project/node_modules/expo-platform-windows/build/index.js', () => mockPlatformData, {
        virtual: true,
      });

      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);

      expect(registry.hasPlatform('windows')).toBe(true);
      expect(registry.getPlatform('windows')).toEqual(mockPlatformData);
    });

    it('should handle packages with default export', async () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';
      const windowsPackagePath = '/project/node_modules/expo-platform-windows';

      mockPath.resolve
        .mockReturnValueOnce(nodeModulesPath)
        .mockReturnValueOnce(windowsPackagePath)
        .mockReturnValueOnce('/project/node_modules/expo-platform-windows/package.json');

      mockFs.existsSync
        .mockReturnValueOnce(true) // node_modules exists
        .mockReturnValueOnce(true); // package.json exists

      mockFs.readdirSync.mockReturnValue(['expo-platform-windows']);

      mockFs.readFileSync.mockReturnValue(
        JSON.stringify({
          name: 'expo-platform-windows',
          main: 'build/index.js',
        })
      );

      mockResolveFrom.mockReturnValue('/project/node_modules/expo-platform-windows/build/index.js');

      // Mock the platform module with default export
      const mockPlatformData = mockPlatformPackage('windows');
      jest.doMock(
        '/project/node_modules/expo-platform-windows/build/index.js',
        () => ({ default: mockPlatformData }),
        { virtual: true }
      );

      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);

      expect(registry.hasPlatform('windows')).toBe(true);
      expect(registry.getPlatform('windows')).toEqual(mockPlatformData);
    });

    it('should skip packages that fail to load', async () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';

      mockPath.resolve.mockReturnValueOnce(nodeModulesPath);
      mockFs.existsSync.mockReturnValueOnce(true);
      mockFs.readdirSync.mockReturnValue(['expo-platform-broken']);

      // Mock a broken package
      mockPath.resolve.mockReturnValueOnce('/project/node_modules/expo-platform-broken');
      mockPath.resolve.mockReturnValueOnce('/project/node_modules/expo-platform-broken/package.json');
      mockFs.existsSync.mockReturnValueOnce(false); // package.json doesn't exist

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);

      expect(registry.getAvailablePlatforms()).toHaveLength(0);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to load external platform package "expo-platform-broken"')
      );

      consoleSpy.mockRestore();
    });

    it('should not load the same package twice', async () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';
      const windowsPackagePath = '/project/node_modules/expo-platform-windows';

      mockPath.resolve
        .mockReturnValue(nodeModulesPath)
        .mockReturnValueOnce(windowsPackagePath)
        .mockReturnValueOnce('/project/node_modules/expo-platform-windows/package.json');

      mockFs.existsSync
        .mockReturnValue(true);

      mockFs.readdirSync.mockReturnValue(['expo-platform-windows']);

      mockFs.readFileSync.mockReturnValue(
        JSON.stringify({
          name: 'expo-platform-windows',
          main: 'build/index.js',
        })
      );

      mockResolveFrom.mockReturnValue('/project/node_modules/expo-platform-windows/build/index.js');

      const mockPlatformData = mockPlatformPackage('windows');
      jest.doMock('/project/node_modules/expo-platform-windows/build/index.js', () => mockPlatformData, {
        virtual: true,
      });

      // Load twice
      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);
      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);

      expect(registry.getAvailablePlatforms()).toHaveLength(1);
      expect(PlatformDiscovery.getLoadedPlatformPackages()).toEqual(['expo-platform-windows']);
    });
  });

  describe('loadExternalPlatformsSync', () => {
    it('should work synchronously', () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';

      mockPath.resolve.mockReturnValueOnce(nodeModulesPath);
      mockFs.existsSync.mockReturnValueOnce(false);

      PlatformDiscovery.loadExternalPlatformsSync(projectRoot, registry);

      expect(registry.getAvailablePlatforms()).toHaveLength(0);
    });
  });

  describe('getLoadedPlatformPackages', () => {
    it('should return empty array initially', () => {
      expect(PlatformDiscovery.getLoadedPlatformPackages()).toEqual([]);
    });

    it('should track loaded packages', async () => {
      const projectRoot = '/project';
      const nodeModulesPath = '/project/node_modules';
      const windowsPackagePath = '/project/node_modules/expo-platform-windows';

      mockPath.resolve
        .mockReturnValueOnce(nodeModulesPath)
        .mockReturnValueOnce(windowsPackagePath)
        .mockReturnValueOnce('/project/node_modules/expo-platform-windows/package.json');

      mockFs.existsSync
        .mockReturnValueOnce(true)
        .mockReturnValueOnce(true);

      mockFs.readdirSync.mockReturnValue(['expo-platform-windows']);

      mockFs.readFileSync.mockReturnValue(
        JSON.stringify({
          name: 'expo-platform-windows',
          main: 'build/index.js',
        })
      );

      mockResolveFrom.mockReturnValue('/project/node_modules/expo-platform-windows/build/index.js');

      const mockPlatformData = mockPlatformPackage('windows');
      jest.doMock('/project/node_modules/expo-platform-windows/build/index.js', () => mockPlatformData, {
        virtual: true,
      });

      await PlatformDiscovery.loadExternalPlatforms(projectRoot, registry);

      expect(PlatformDiscovery.getLoadedPlatformPackages()).toEqual(['expo-platform-windows']);
    });
  });

  describe('clearLoadedPlatforms', () => {
    it('should clear the loaded platforms cache', () => {
      // Simulate some loaded platforms
      PlatformDiscovery['loadedPlatforms'].add('expo-platform-windows');
      PlatformDiscovery['loadedPlatforms'].add('expo-platform-macos');

      expect(PlatformDiscovery.getLoadedPlatformPackages()).toHaveLength(2);

      PlatformDiscovery.clearLoadedPlatforms();

      expect(PlatformDiscovery.getLoadedPlatformPackages()).toHaveLength(0);
    });
  });
});
