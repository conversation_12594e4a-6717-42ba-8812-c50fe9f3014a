{"name": "expo-platform-windows-template", "version": "1.0.0", "description": "Native project template for Windows platform", "scripts": {"build": "msbuild windows/*.sln", "start": "react-native run-windows", "clean": "rm -rf windows/build"}, "dependencies": {"react": "*", "react-native": "*", "react-native-windows": "*"}, "devDependencies": {"@expo/config-plugins": "*", "@react-native-windows/cli": "*"}, "expo": {"platform": "windows", "template": true}, "keywords": ["expo", "platform", "template", "windows", "native", "react-native-windows"]}