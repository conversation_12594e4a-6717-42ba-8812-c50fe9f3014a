import { platformRegistry } from './PlatformRegistry';

/**
 * Configuration for platform-specific debugging tools
 */
export interface PlatformDebuggingConfiguration {
  /** Custom debugger endpoints for the platform */
  debuggerEndpoints?: {
    [endpointName: string]: {
      path: string;
      handler: string;
      description: string;
    };
  };

  /** Platform-specific inspector tools */
  inspectorTools?: {
    [toolName: string]: {
      name: string;
      description: string;
      implementation: string;
      icon?: string;
      category?: 'performance' | 'network' | 'ui' | 'memory' | 'storage';
    };
  };

  /** Custom profiling tools */
  profilingTools?: {
    [toolName: string]: {
      name: string;
      description: string;
      implementation: string;
      outputFormat: 'json' | 'csv' | 'trace' | 'custom';
      category?: 'cpu' | 'memory' | 'network' | 'rendering' | 'custom';
    };
  };

  /** Platform-specific error handlers */
  errorHandlers?: {
    [errorType: string]: {
      handler: string;
      priority: number;
      description: string;
    };
  };

  /** Custom log formatters */
  logFormatters?: {
    [logType: string]: {
      formatter: string;
      color?: string;
      icon?: string;
    };
  };

  /** Platform-specific breakpoint handlers */
  breakpointHandlers?: {
    [handlerType: string]: {
      handler: string;
      supportedFileTypes: string[];
      description: string;
    };
  };
}

/**
 * Advanced debugging integration for external platforms.
 * Provides comprehensive debugging tools, profiling, and error handling.
 */
export class AdvancedDebuggingIntegration {
  /**
   * Get debugging configuration for a specific platform.
   * @param platform The platform identifier
   * @returns Platform debugging configuration or undefined
   */
  static getPlatformDebuggingConfig(platform: string): PlatformDebuggingConfiguration | undefined {
    const platformData = platformRegistry.getPlatform(platform);
    return platformData?.debuggingConfig;
  }

  /**
   * Get all platforms with debugging configurations.
   * @returns Array of platform names that have debugging configurations
   */
  static getPlatformsWithDebugging(): string[] {
    return platformRegistry
      .getAvailablePlatforms()
      .filter((platform) => this.getPlatformDebuggingConfig(platform) !== undefined);
  }

  /**
   * Generate debugger endpoint configuration for Metro middleware.
   * @param projectRoot The project root directory
   * @returns Object mapping endpoint paths to handler configurations
   */
  static generateDebuggerEndpoints(projectRoot: string): Record<string, any> {
    const endpoints: Record<string, any> = {};
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.debuggerEndpoints) {
        for (const [endpointName, endpointConfig] of Object.entries(config.debuggerEndpoints)) {
          const fullPath = `/inspector/${platform}/${endpointConfig.path}`;
          endpoints[fullPath] = {
            platform,
            endpointName,
            handler: endpointConfig.handler,
            description: endpointConfig.description,
            projectRoot,
          };
        }
      }
    }

    return endpoints;
  }

  /**
   * Get aggregated inspector tools from all platforms.
   * @returns Map of tool names to platform and tool configuration
   */
  static getAggregatedInspectorTools(): Map<string, { platform: string; config: any }> {
    const tools = new Map<string, { platform: string; config: any }>();
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.inspectorTools) {
        for (const [toolName, toolConfig] of Object.entries(config.inspectorTools)) {
          const uniqueToolName = `${platform}.${toolName}`;
          tools.set(uniqueToolName, { platform, config: toolConfig });
        }
      }
    }

    return tools;
  }

  /**
   * Get aggregated profiling tools from all platforms.
   * @returns Map of tool names to platform and tool configuration
   */
  static getAggregatedProfilingTools(): Map<string, { platform: string; config: any }> {
    const tools = new Map<string, { platform: string; config: any }>();
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.profilingTools) {
        for (const [toolName, toolConfig] of Object.entries(config.profilingTools)) {
          const uniqueToolName = `${platform}.${toolName}`;
          tools.set(uniqueToolName, { platform, config: toolConfig });
        }
      }
    }

    return tools;
  }

  /**
   * Generate error handler configuration for debugging middleware.
   * @returns Array of error handlers sorted by priority
   */
  static generateErrorHandlerConfig(): { platform: string; errorType: string; config: any }[] {
    const handlers: { platform: string; errorType: string; config: any; priority: number }[] = [];
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.errorHandlers) {
        for (const [errorType, handlerConfig] of Object.entries(config.errorHandlers)) {
          handlers.push({
            platform,
            errorType,
            config: handlerConfig,
            priority: handlerConfig.priority,
          });
        }
      }
    }

    // Sort by priority (higher priority first)
    return handlers.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Get log formatters for all platforms.
   * @returns Map of log types to platform and formatter configuration
   */
  static getLogFormatters(): Map<string, { platform: string; config: any }> {
    const formatters = new Map<string, { platform: string; config: any }>();
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.logFormatters) {
        for (const [logType, formatterConfig] of Object.entries(config.logFormatters)) {
          const uniqueLogType = `${platform}.${logType}`;
          formatters.set(uniqueLogType, { platform, config: formatterConfig });
        }
      }
    }

    return formatters;
  }

  /**
   * Get breakpoint handlers for all platforms.
   * @returns Map of handler types to platform and handler configuration
   */
  static getBreakpointHandlers(): Map<string, { platform: string; config: any }> {
    const handlers = new Map<string, { platform: string; config: any }>();
    const platforms = this.getPlatformsWithDebugging();

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config?.breakpointHandlers) {
        for (const [handlerType, handlerConfig] of Object.entries(config.breakpointHandlers)) {
          const uniqueHandlerType = `${platform}.${handlerType}`;
          handlers.set(uniqueHandlerType, { platform, config: handlerConfig });
        }
      }
    }

    return handlers;
  }

  /**
   * Generate debugging tools documentation for a platform.
   * @param platform The platform identifier
   * @returns Markdown documentation string
   */
  static generateDebuggingDocumentation(platform: string): string {
    const config = this.getPlatformDebuggingConfig(platform);
    const platformData = platformRegistry.getPlatform(platform);

    if (!config) {
      return `# ${platformData?.displayName || platform} Platform Debugging\n\nNo debugging tools configured for this platform.`;
    }

    let doc = `# ${platformData?.displayName || platform} Platform Debugging\n\n`;

    if (config.debuggerEndpoints && Object.keys(config.debuggerEndpoints).length > 0) {
      doc += '## Debugger Endpoints\n\n';
      for (const [name, endpoint] of Object.entries(config.debuggerEndpoints)) {
        doc += `### \`${name}\`\n`;
        doc += `- **Path**: \`/inspector/${platform}/${endpoint.path}\`\n`;
        doc += `- **Description**: ${endpoint.description}\n`;
        doc += `- **Handler**: \`${endpoint.handler}\`\n\n`;
      }
    }

    if (config.inspectorTools && Object.keys(config.inspectorTools).length > 0) {
      doc += '## Inspector Tools\n\n';
      for (const [, tool] of Object.entries(config.inspectorTools)) {
        doc += `### ${tool.name}\n`;
        doc += `- **Description**: ${tool.description}\n`;
        doc += `- **Category**: ${tool.category || 'general'}\n`;
        doc += `- **Implementation**: \`${tool.implementation}\`\n`;
        if (tool.icon) doc += `- **Icon**: ${tool.icon}\n`;
        doc += '\n';
      }
    }

    if (config.profilingTools && Object.keys(config.profilingTools).length > 0) {
      doc += '## Profiling Tools\n\n';
      for (const [, tool] of Object.entries(config.profilingTools)) {
        doc += `### ${tool.name}\n`;
        doc += `- **Description**: ${tool.description}\n`;
        doc += `- **Category**: ${tool.category || 'general'}\n`;
        doc += `- **Output Format**: ${tool.outputFormat}\n`;
        doc += `- **Implementation**: \`${tool.implementation}\`\n\n`;
      }
    }

    return doc;
  }

  /**
   * Generate comprehensive debugging report for all platforms.
   * @returns Object with debugging statistics and configurations
   */
  static generateDebuggingReport(): {
    summary: {
      totalPlatforms: number;
      platformsWithDebugging: number;
      totalInspectorTools: number;
      totalProfilingTools: number;
      totalErrorHandlers: number;
    };
    platforms: Record<
      string,
      {
        debuggerEndpoints: number;
        inspectorTools: number;
        profilingTools: number;
        errorHandlers: number;
        logFormatters: number;
        breakpointHandlers: number;
      }
    >;
  } {
    const platforms = this.getPlatformsWithDebugging();
    const report = {
      summary: {
        totalPlatforms: platformRegistry.getAvailablePlatforms().length,
        platformsWithDebugging: platforms.length,
        totalInspectorTools: 0,
        totalProfilingTools: 0,
        totalErrorHandlers: 0,
      },
      platforms: {} as Record<string, any>,
    };

    for (const platform of platforms) {
      const config = this.getPlatformDebuggingConfig(platform);
      if (config) {
        const platformReport = {
          debuggerEndpoints: Object.keys(config.debuggerEndpoints || {}).length,
          inspectorTools: Object.keys(config.inspectorTools || {}).length,
          profilingTools: Object.keys(config.profilingTools || {}).length,
          errorHandlers: Object.keys(config.errorHandlers || {}).length,
          logFormatters: Object.keys(config.logFormatters || {}).length,
          breakpointHandlers: Object.keys(config.breakpointHandlers || {}).length,
        };

        report.platforms[platform] = platformReport;
        report.summary.totalInspectorTools += platformReport.inspectorTools;
        report.summary.totalProfilingTools += platformReport.profilingTools;
        report.summary.totalErrorHandlers += platformReport.errorHandlers;
      }
    }

    return report;
  }
}
