import type { ConfigT } from 'metro-config';

/**
 * Extends Metro configuration to support external platforms.
 * This function attempts to integrate with the platform registry when available,
 * falling back gracefully when the CLI is not available.
 *
 * This function is safe to call multiple times - it will detect if external platforms
 * have already been added and avoid duplicates.
 */
export function withExternalPlatforms(config: ConfigT, platformData?: any): ConfigT {
  if (process.env.EXPO_DEBUG) {
    console.debug('Metro: withExternalPlatforms called');
  }

  // If platform data is passed directly, use it
  if (platformData) {
    if (process.env.EXPO_DEBUG) {
      console.debug('Metro: Using provided platform data');
    }
    return applyExternalPlatformConfiguration(config, platformData);
  }

  try {
    // Dynamic import to avoid circular dependencies and handle cases where CLI is not available
    // Try the new platform registry package first, then fallback to CLI
    let platformRegistryModule;
    try {
      // Try new platform registry package first
      platformRegistryModule = require('@expo/platform-registry');
      if (process.env.EXPO_DEBUG) {
        console.debug('Metro: Loaded platform registry from @expo/platform-registry');
      }
    } catch (registryError: any) {
      if (process.env.EXPO_DEBUG) {
        console.debug('Metro: @expo/platform-registry failed:', registryError.message);
      }
      try {
        // Fallback to CLI built path (production)
        platformRegistryModule = require('@expo/cli/build/src/core/PlatformRegistry');
        if (process.env.EXPO_DEBUG) {
          console.debug('Metro: Loaded platform registry from CLI built path');
        }
      } catch (builtError: any) {
        if (process.env.EXPO_DEBUG) {
          console.debug('Metro: CLI built path failed:', builtError.message);
        }
        try {
          // Fallback to CLI source path (development)
          platformRegistryModule = require('@expo/cli/src/core/PlatformRegistry');
          if (process.env.EXPO_DEBUG) {
            console.debug('Metro: Loaded platform registry from CLI source path');
          }
        } catch (sourceError: any) {
          if (process.env.EXPO_DEBUG) {
            console.debug('Metro: CLI source path failed:', sourceError.message);
          }
          throw sourceError;
        }
      }
    }

    if (platformRegistryModule?.platformRegistry && platformRegistryModule?.PlatformDiscovery) {
      if (process.env.EXPO_DEBUG) {
        console.debug('Metro: Platform registry available, loading external platforms');
      }

      // Load external platforms in Metro context
      // This is necessary because Metro runs in a separate context from CLI commands
      try {
        // Use synchronous loading to avoid async issues in Metro config
        const projectRoot = process.cwd();
        platformRegistryModule.PlatformDiscovery.loadExternalPlatformsSync(projectRoot);

        if (process.env.EXPO_DEBUG) {
          console.debug('Metro: External platforms loaded, applying configuration');
        }
      } catch (loadError: any) {
        if (process.env.EXPO_DEBUG) {
          console.debug('Metro: Failed to load external platforms:', loadError.message);
        }
      }

      return applyExternalPlatformConfiguration(config, platformRegistryModule.platformRegistry);
    } else {
      if (process.env.EXPO_DEBUG) {
        console.debug('Metro: Platform registry module loaded but platformRegistry or PlatformDiscovery not found');
      }
    }
  } catch (error: any) {
    // Platform registry not available (e.g., in web environments, or when CLI is not installed)
    // This is expected and we should gracefully fall back to the original config
    if (process.env.NODE_ENV === 'development' || process.env.EXPO_DEBUG) {
      console.debug('Metro: Platform registry not available, using built-in platforms only');
      console.debug('Metro: Platform registry error:', error.message);
    }
  }

  return config;
}

/**
 * Generate Router-specific file extensions for external platforms.
 * This ensures that Expo Router can resolve platform-specific route files.
 */
function generateRouterExtensions(platforms: string[]): string[] {
  const routerExtensions: string[] = [];
  const baseExtensions = ['js', 'jsx', 'ts', 'tsx'];

  for (const platform of platforms) {
    for (const ext of baseExtensions) {
      routerExtensions.push(`.${platform}.${ext}`);
    }
  }

  return routerExtensions;
}

/**
 * Apply external platform configuration to Metro config.
 * This is separated to allow for easier testing and to avoid repeated code.
 * Includes validation to prevent conflicts and duplicate additions.
 */
function applyExternalPlatformConfiguration(config: ConfigT, platformRegistry: any): ConfigT {
  const externalPlatforms = platformRegistry.getAvailablePlatforms();

  if (process.env.EXPO_DEBUG) {
    console.debug('Metro: External platforms from registry:', externalPlatforms);
  }

  if (externalPlatforms.length === 0) {
    if (process.env.EXPO_DEBUG) {
      console.debug('Metro: No external platforms available, returning original config');
    }
    return config;
  }

  // Get current configuration
  const currentPlatforms = config.resolver?.platforms || ['ios', 'android'];
  const currentSourceExts = config.resolver?.sourceExts || [];

  // Check if external platforms have already been added to avoid duplicates
  const hasExternalPlatforms = externalPlatforms.some((platform: string) =>
    currentPlatforms.includes(platform)
  );

  if (hasExternalPlatforms && process.env.EXPO_DEBUG) {
    console.debug('Metro: External platforms already configured, skipping duplicate addition');
    return config;
  }

  // Add external platforms to Metro's platform list (deduplicated)
  const allPlatforms = [...new Set([...currentPlatforms, ...externalPlatforms])];

  // Collect all Metro extensions from external platforms
  const externalExtensions = new Set<string>();

  for (const platform of externalPlatforms) {
    const platformData = platformRegistry.getPlatform(platform);
    if (platformData?.metroExtensions) {
      platformData.metroExtensions.forEach((ext: string) => {
        // Validate extension format
        if (typeof ext === 'string' && ext.startsWith('.')) {
          externalExtensions.add(ext);
        } else if (process.env.EXPO_DEBUG) {
          console.warn(
            `Metro: Invalid extension "${ext}" from platform ${platform}, must start with "."`
          );
        }
      });
    }
  }

  // Add external platform extensions to source extensions (deduplicated)
  const allSourceExts = [...new Set([...currentSourceExts, ...Array.from(externalExtensions)])];

  // Generate Router-specific extensions for external platforms
  const routerExtensions = generateRouterExtensions(externalPlatforms);
  const allRouterSourceExts = [...new Set([...allSourceExts, ...routerExtensions])];

  // Only log if we're actually adding new platforms
  const newPlatforms = externalPlatforms.filter(
    (platform: string) => !currentPlatforms.includes(platform)
  );
  const newExtensions = Array.from(externalExtensions).filter(
    (ext: string) => !currentSourceExts.includes(ext)
  );

  if (process.env.EXPO_DEBUG && newPlatforms.length > 0) {
    console.log(`Metro: Added external platforms: ${newPlatforms.join(', ')}`);
    if (newExtensions.length > 0) {
      console.log(`Metro: Added external extensions: ${newExtensions.join(', ')}`);
    }
    if (routerExtensions.length > 0) {
      console.log(`Metro: Added Router extensions: ${routerExtensions.join(', ')}`);
    }
  }

  return {
    ...config,
    resolver: {
      ...config.resolver,
      platforms: allPlatforms,
      sourceExts: allRouterSourceExts,
    },
  };
}
