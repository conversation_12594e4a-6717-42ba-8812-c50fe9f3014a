import chalk from 'chalk';

import { platformRegistry } from '../../core/PlatformRegistry';
import * as Log from '../../log';

export interface PlatformKeyMapping {
  platform: string;
  key: string;
  displayName: string;
  isBuiltIn: boolean;
  hasConflict: boolean;
  conflictsWith?: string[];
}

/**
 * Smart key assignment algorithm for platform shortcuts in the start command interface.
 *
 * Priority order:
 * 1. Built-in platforms get their traditional keys (i=iOS, a=Android, w=Web)
 * 2. External platforms try first letter of platform name
 * 3. If conflict, try second letter, then third, etc.
 * 4. If still conflict, use numbers (1, 2, 3, etc.)
 * 5. If all numbers taken, use special characters
 *
 * This ensures every platform gets a unique key while maintaining backward compatibility.
 */
export function assignPlatformKeys(availablePlatforms: string[]): PlatformKeyMapping[] {
  const mappings: PlatformKeyMapping[] = [];
  const usedKeys = new Set<string>();

  // Step 1: Assign built-in platform keys first (these have priority)
  const builtInMappings = [
    { platform: 'ios', key: 'i', displayName: 'iOS' },
    { platform: 'android', key: 'a', displayName: 'Android' },
    { platform: 'web', key: 'w', displayName: 'Web' },
  ];

  for (const builtIn of builtInMappings) {
    if (availablePlatforms.includes(builtIn.platform)) {
      mappings.push({
        platform: builtIn.platform,
        key: builtIn.key,
        displayName: builtIn.displayName,
        isBuiltIn: true,
        hasConflict: false,
      });
      usedKeys.add(builtIn.key);
    }
  }

  // Step 2: Assign keys for external platforms
  const externalPlatforms = platformRegistry.getAvailablePlatforms();

  for (const platform of externalPlatforms) {
    if (availablePlatforms.includes(platform)) {
      const platformData = platformRegistry.getPlatform(platform);
      const displayName =
        platformData?.displayName || platform.charAt(0).toUpperCase() + platform.slice(1);

      const assignedKey = findAvailableKey(platform, usedKeys);

      mappings.push({
        platform,
        key: assignedKey,
        displayName,
        isBuiltIn: false,
        hasConflict: false, // Will be updated if conflicts are detected
      });
      usedKeys.add(assignedKey);
    }
  }

  // Step 3: Detect and mark conflicts (for logging purposes)
  detectConflicts(mappings);

  return mappings;
}

/**
 * Find an available key for a platform using intelligent fallback strategy.
 */
function findAvailableKey(platform: string, usedKeys: Set<string>): string {
  // Strategy 1: Try each letter of the platform name
  for (let i = 0; i < platform.length; i++) {
    const letter = platform.charAt(i).toLowerCase();
    if (letter.match(/[a-z]/) && !usedKeys.has(letter)) {
      return letter;
    }
  }

  // Strategy 2: Try numbers 1-9
  for (let i = 1; i <= 9; i++) {
    const number = i.toString();
    if (!usedKeys.has(number)) {
      return number;
    }
  }

  // Strategy 3: Try special characters (fallback for extreme cases)
  const specialChars = ['0', '-', '=', '[', ']', ';', "'", ',', '.', '/'];
  for (const char of specialChars) {
    if (!usedKeys.has(char)) {
      return char;
    }
  }

  // Strategy 4: Ultimate fallback - use platform name with prefix
  return `p${platform.charAt(0)}`;
}

/**
 * Detect conflicts between platform keys (for logging and user feedback).
 */
function detectConflicts(mappings: PlatformKeyMapping[]): void {
  const keyGroups = new Map<string, PlatformKeyMapping[]>();

  // Group mappings by key
  for (const mapping of mappings) {
    if (!keyGroups.has(mapping.key)) {
      keyGroups.set(mapping.key, []);
    }
    keyGroups.get(mapping.key)!.push(mapping);
  }

  // Mark conflicts
  for (const [, group] of keyGroups) {
    if (group.length > 1) {
      const conflictingPlatforms = group.map((m) => m.platform);
      for (const mapping of group) {
        mapping.hasConflict = true;
        mapping.conflictsWith = conflictingPlatforms.filter((p) => p !== mapping.platform);
      }
    }
  }
}

/**
 * Get the key mapping for a specific platform.
 */
export function getPlatformKey(platform: string, mappings: PlatformKeyMapping[]): string | null {
  const mapping = mappings.find((m) => m.platform === platform);
  return mapping?.key || null;
}

/**
 * Find platform by key from mappings.
 */
export function findPlatformByKey(key: string, mappings: PlatformKeyMapping[]): string | null {
  const mapping = mappings.find((m) => m.key.toLowerCase() === key.toLowerCase());
  return mapping?.platform || null;
}

/**
 * Log key assignment conflicts and resolutions for debugging.
 */
export function logKeyAssignments(mappings: PlatformKeyMapping[]): void {
  const conflicts = mappings.filter((m) => m.hasConflict);

  if (conflicts.length > 0) {
    Log.warn(chalk.yellow('Platform key assignment conflicts detected:'));
    for (const conflict of conflicts) {
      Log.warn(
        chalk.gray(
          `  Platform "${conflict.platform}" assigned key "${conflict.key}" ` +
            `(conflicts with: ${conflict.conflictsWith?.join(', ')})`
        )
      );
    }
  }

  // Log all assignments in debug mode
  if (process.env.EXPO_DEBUG) {
    Log.log(chalk.gray('Platform key assignments:'));
    for (const mapping of mappings) {
      const status = mapping.isBuiltIn ? 'built-in' : 'external';
      const conflict = mapping.hasConflict ? ' (conflict)' : '';
      Log.log(chalk.gray(`  ${mapping.key} → ${mapping.displayName} (${status}${conflict})`));
    }
  }
}
