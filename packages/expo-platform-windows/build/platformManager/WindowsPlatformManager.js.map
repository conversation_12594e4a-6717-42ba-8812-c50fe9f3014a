{"version": 3, "file": "WindowsPlatformManager.js", "sourceRoot": "", "sources": ["../../src/platformManager/WindowsPlatformManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;GAUG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,yFAI6D;AAC7D,0FAA4E;AAC5E,4FAA8E;AAC9E,6GAAqF;AACrF,mGAA2E;AAC3E,iDAA4C;AAC5C,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAiC;AAKjC,0EAAuE;AAEvE,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AA8BlC;;;;;GAKG;AACH,MAAa,sBAAuB,SAAQ,iCAG3C;IACC,YACE,WAAmB,EACnB,OAQC;QAED,KAAK,CAAC,WAAW,EAAE;YACjB,QAAQ,EAAE,SAAgB;YAC1B,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IACD;;OAEG;IACH,KAAK,CAAC,SAAS,CACb,OAE8F,EAC9F,eAAgE;QAEhE,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YAChC,6CAA6C;YAC7C,MAAM,cAAc,GAA+B;gBACjD,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,GAAG,OAAO,CAAC,KAAK;aACjB,CAAC;YAEF,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAE1C,4CAA4C;YAC5C,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE,CAAC;SACvC;aAAM;YACL,+CAA+C;YAC/C,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;SAClD;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,OAAmC;QAC9D,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAEhC,2CAA2C;QAC3C,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjC,oDAAoD;QACpD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;QAEhE,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,mBAAmB,CAAC,CAAC;QAEtF,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SACzC;aAAM;YACL,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE;YAC1B,IAAI,EAAE,KAAK;YACX,aAAa,EAAE,OAAO;YACtB,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,KAAK;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,8DAA8D;QAC9D,IAAI;YACF,MAAM,UAAU,GAAG,sBAAY,CAAC,oBAAoB,CAAC,KAAY,EAAE,KAAK,CAAC,CAAC;YAC1E,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;aAC7F;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,oBAAoB,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC;aACnE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACpD;QAED,kEAAkE;QAClE,IAAI;YACF,MAAM,WAAW,GAAG,sBAAY,CAAC,0BAA0B,EAAE,CAAC;YAC9D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5B,QAAQ,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;aACxF;iBAAM;gBACL,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3E,OAAO,CAAC,GAAG,CAAC,wBAAwB,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAEhE,qCAAqC;gBACrC,MAAM,UAAU,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;oBAClC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,aAAa,CAAC,QAAQ,EAAE,2DAA2D,CAAC,CAAC;iBAC3H;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,IAAI,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC1D;QAED,4BAA4B;QAC5B,IAAI;YACF,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;YACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,YAAY,GAAG,EAAE,EAAE;gBACrB,QAAQ,CAAC,IAAI,CAAC,WAAW,WAAW,iDAAiD,CAAC,CAAC;aACxF;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,sBAAsB,WAAW,EAAE,CAAC,CAAC;aAClD;SACF;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;SAC/C;QAED,qCAAqC;QACrC,IAAI;YACF,MAAM,SAAS,CAAC,4BAA4B,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;SACvG;QAED,yCAAyC;QACzC,IAAI;YACF,qCAAqC;YACrC,MAAM,aAAa,GAAG,MAAM,SAAS,CAAC,qHAAqH,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAChL,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACzC,QAAQ,CAAC,IAAI,CAAC,wGAAwG,CAAC,CAAC;aACzH;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;aACjD;SACF;QAAC,OAAO,KAAK,EAAE;YACd,QAAQ,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;SACjE;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QAEpC,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;SACpE;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACnE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC;SACtD;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE,CAAC,CAAC,CAAC;SAC9D;QAED,OAAO;YACL,OAAO;YACP,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAmC;QACnE,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAEhC,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAChF;QAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,6BAA6B;QAC7B,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACjD;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC5C;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SACvC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAmC;QACnE,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAEhC,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAChF;QAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,6BAA6B;QAC7B,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACjD;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC5C;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;SACpD;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SACvC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAmB,EAAE,SAAkB;QAC9D,IAAI,SAAS,EAAE;YACb,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBAC7B,OAAO,UAAU,CAAC;aACnB;SACF;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;QAED,2CAA2C;QAC3C,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACzC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAE5D,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,OAAmC;QAC9E,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC;QACvD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC;QAEnC,IAAI;YACF,8DAA8D;YAC9D,MAAM,UAAU,GAAG,sBAAY,CAAC,oBAAoB,CAAC,IAAW,EAAE,KAAK,CAAC,CAAC;YACzE,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,IAAI,EAAE,CAAC,CAAC;aACrE;YAED,2BAA2B;YAC3B,MAAM,YAAY,GAA2B,EAAE,CAAC;YAChD,IAAI,OAAO,CAAC,YAAY,EAAE;gBACxB,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC9C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACrB,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrC,IAAI,GAAG,IAAI,KAAK,EAAE;wBAChB,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;qBACzC;gBACH,CAAC,CAAC,CAAC;aACJ;YAED,kDAAkD;YAClD,MAAM,KAAK,CAAC,aAAa,CACvB,UAAU,EACV,OAAO,EACP,aAAoB,EACpB,IAAW,EACX,YAAY,EACZ,KAAK,EAAE,UAAU;YACjB,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,oBAAoB;YAC/B,KAAK,CAAC,aAAa;aACpB,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;SACrD;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAC1B,WAAmB,EACnB,OAAmC;QAEnC,IAAI;YACF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAEjD,8BAA8B;YAC9B,MAAM,WAAW,GAAG,IAAI,uCAAkB,EAAE,CAAC;YAE7C,6BAA6B;YAC7B,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YAEvE,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;YAE3B,yDAAyD;YACzD,IAAI;gBACF,MAAM,IAAI,CAAC,gCAAgC,CAAC,WAAW,CAAC,CAAC;aAC1D;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,IAAI,CAAC,qGAAqG,CAAC,CAAC;aACrH;SAEF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gCAAgC,CAAC,WAAmB;QAChE,IAAI;YACF,8CAA8C;YAC9C,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,mCAAmC,EAAE;gBAClE,GAAG,EAAE,WAAW;gBAChB,OAAO,EAAE,KAAK,EAAE,oBAAoB;aACrC,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;aAC3E;SACF;QAAC,OAAO,KAAK,EAAE;YACd,gEAAgE;YAChE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,WAAmB,EACnB,OAAmC;;QAEnC,IAAI;YACF,kEAAkE;YAClE,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,SAAS,EAAE,SAAS;wBACpB,YAAY,EAAE,CAAA,MAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,0CAAE,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,KAAI,EAAE;wBAC7E,OAAO,EAAE;4BACP,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;4BACvC,WAAW,EAAE,EAAE;yBAChB;qBACF;iBACF;aACF,CAAC;YAEF,iEAAiE;YACjE,MAAM,aAAa,GAAG;gBACpB,GAAG,OAAO;gBACV,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;gBAC3B,OAAO,EAAE,OAAO,CAAC,aAAa,KAAK,SAAS;gBAC5C,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;gBACxD,oEAAoE;gBACpE,MAAM,MAAM,CAAC,cAAc,CAAC,aAAoB,EAAE,KAAK,EAAE,MAAa,CAAC,CAAC;aACzE;iBAAM;gBACL,8DAA8D;gBAC9D,MAAM,UAAU,GAAG,sBAAY,CAAC,oBAAoB,CAAC,aAAa,CAAC,IAAW,EAAE,KAAK,CAAC,CAAC;gBACvF,IAAI,CAAC,UAAU,EAAE;oBACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;iBACnF;gBAED,MAAM,MAAM,CAAC,eAAe,CAAC,aAAoB,EAAE,KAAK,EAAE,MAAa,EAAE,UAAU,CAAC,CAAC;aACtF;YAED,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;SAC1D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,WAAmB;QAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,IAAI;YACF,iCAAiC;YACjC,MAAM,YAAY,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;gBACnE,GAAG,EAAE,WAAW;gBAChB,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,WAAmB;QACrD,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CACxC,WAAW,EACX,SAAS,EACT,4BAA4B,CAC7B,CAAC;QAEF,IAAI,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE;YAC3C,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YAClE,OAAO,OAAO,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC;SAC1D;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAmC;QAC9D,OAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;IAC1F,CAAC;CACF;AA7dD,wDA6dC"}