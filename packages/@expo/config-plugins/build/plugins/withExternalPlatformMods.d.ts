import { ExportedConfig, Mod } from '../Plugin.types';
import { ForwardedBaseModOptions } from './createBaseMod';
/**
 * External platform mod support for config plugins.
 * This enables external platforms to register their own mods and base mods.
 */
/**
 * Generic external platform mod configuration.
 * External platforms can extend this interface using TypeScript declaration merging.
 */
export interface ExternalPlatformModConfig {
    /** Dangerously make a modification before any other platform mods have been run. */
    dangerous?: Mod<unknown>;
    /** Dangerously make a modification after all the other platform mods have been run. */
    finalized?: Mod<unknown>;
    /** Platform-specific manifest or configuration file */
    manifest?: Mod<any>;
    /** Platform-specific configuration file */
    config?: Mod<any>;
    /** Platform-specific project file */
    project?: Mod<any>;
    /** Platform-specific build configuration */
    build?: Mod<any>;
    /** Platform-specific resources */
    resources?: Mod<any>;
    /** Platform-specific assets */
    assets?: Mod<any>;
    /** Platform-specific dependencies */
    dependencies?: Mod<any>;
    /** Platform-specific permissions */
    permissions?: Mod<any>;
    /** Platform-specific entitlements */
    entitlements?: Mod<any>;
    /** Platform-specific metadata */
    metadata?: Mod<any>;
}
/**
 * Plugin to add external platform base mods to the config.
 * This automatically discovers and registers base mods for all available external platforms.
 */
export declare function withExternalPlatformBaseMods(config: ExportedConfig, props?: ForwardedBaseModOptions): ExportedConfig;
/**
 * Plugin to add base mods for a specific external platform.
 */
export declare function withExternalPlatformBaseMod(config: ExportedConfig, { platform, ...props }: ForwardedBaseModOptions & {
    platform: string;
}): ExportedConfig;
/**
 * Helper function to check if a platform is an external platform.
 */
export declare function isExternalPlatform(platform: string): boolean;
/**
 * Get all available platforms including external ones.
 */
export declare function getAllAvailablePlatforms(): string[];
