#pragma once

#include "App.xaml.g.h"

namespace winrt::HelloWorld::implementation
{
    struct App : AppT<App>
    {
        App() noexcept;
        void OnLaunched(Windows::ApplicationModel::Activation::LaunchActivatedEventArgs const&);
        void OnSuspending(IInspectable const&, Windows::ApplicationModel::SuspendingEventArgs const&);
        void OnNavigationFailed(IInspectable const&, Windows::UI::Xaml::Navigation::NavigationFailedEventArgs const&);
    private:
        using super = AppT<App>;
    };
}

namespace winrt::HelloWorld::factory_implementation
{
    struct App : AppT<App, implementation::App>
    {
    };
}
