"use strict";
/**
 * Windows Platform Prerequisites
 *
 * Provides health checking and prerequisite validation for Windows development.
 * This implementation checks for required tools, SDKs, and development environment setup.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WindowsPrerequisite = void 0;
const PlatformRegistry_1 = require("@expo/cli/src/core/PlatformRegistry");
const child_process_1 = require("child_process");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Windows platform prerequisite checker.
 * Validates the Windows development environment including Visual Studio,
 * Windows SDK, and react-native-windows dependencies.
 */
class WindowsPrerequisite extends PlatformRegistry_1.ExternalPlatformPrerequisite {
    constructor(platform) {
        super(platform);
    }
    /**
     * Check if the development environment is properly set up for Windows development.
     */
    async checkDevelopmentEnvironment() {
        try {
            // Check if we're on Windows
            if (process.platform !== 'win32') {
                return false;
            }
            // Check for Node.js (should be available if we're running this)
            const nodeVersion = process.version;
            if (!nodeVersion) {
                return false;
            }
            // Check Node.js version (require 16+)
            const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
            if (majorVersion < 16) {
                return false;
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Check if system requirements are met for Windows development.
     */
    async checkSystemRequirements() {
        try {
            // Check Windows version
            const windowsVersion = await this.getWindowsVersion();
            if (!windowsVersion || !this.isWindowsVersionSupported(windowsVersion)) {
                return false;
            }
            // Check for PowerShell
            try {
                (0, child_process_1.execSync)('powershell -Command "Get-Host"', { stdio: 'pipe' });
            }
            catch (error) {
                return false;
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Check if platform-specific tools are installed and available.
     */
    async checkPlatformTools() {
        try {
            // Check for Visual Studio or Build Tools
            const hasVisualStudio = await this.checkVisualStudio();
            if (!hasVisualStudio) {
                return false;
            }
            // Check for Windows SDK
            const hasWindowsSDK = await this.checkWindowsSDK();
            if (!hasWindowsSDK) {
                return false;
            }
            // Check for MSBuild
            const hasMSBuild = await this.checkMSBuild();
            if (!hasMSBuild) {
                return false;
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Get installation instructions for missing prerequisites.
     */
    getInstallationInstructions() {
        const instructions = [];
        if (process.platform !== 'win32') {
            instructions.push('Windows development requires a Windows machine');
            return instructions;
        }
        instructions.push('Install Visual Studio 2019 or 2022 with the following workloads:');
        instructions.push('  • Desktop development with C++');
        instructions.push('  • Universal Windows Platform development');
        instructions.push('');
        instructions.push('Or install Visual Studio Build Tools 2019/2022 with:');
        instructions.push('  • C++ build tools');
        instructions.push('  • Windows 10/11 SDK (latest version)');
        instructions.push('');
        instructions.push('Download from: https://visualstudio.microsoft.com/downloads/');
        instructions.push('');
        instructions.push('For react-native-windows setup, run:');
        instructions.push('  npx @react-native-windows/cli@latest init');
        instructions.push('');
        instructions.push('For detailed setup instructions, visit:');
        instructions.push('  https://microsoft.github.io/react-native-windows/docs/rnw-dependencies');
        return instructions;
    }
    /**
     * Get Windows version information.
     */
    async getWindowsVersion() {
        try {
            const output = (0, child_process_1.execSync)('ver', { encoding: 'utf8', stdio: 'pipe' });
            return output.trim();
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Check if the Windows version is supported.
     */
    isWindowsVersionSupported(version) {
        // Support Windows 10 version 1903 (build 18362) and later
        // Support Windows 11 (all versions)
        const buildMatch = version.match(/\[Version (\d+)\.(\d+)\.(\d+)/);
        if (!buildMatch) {
            return false;
        }
        const major = parseInt(buildMatch[1]);
        const minor = parseInt(buildMatch[2]);
        const build = parseInt(buildMatch[3]);
        // Windows 10 (major version 10)
        if (major === 10 && minor === 0) {
            return build >= 18362; // Windows 10 version 1903
        }
        // Windows 11 and future versions
        if (major >= 10) {
            return true;
        }
        return false;
    }
    /**
     * Check for Visual Studio installation.
     */
    async checkVisualStudio() {
        try {
            // Check for Visual Studio using vswhere
            const vsWherePath = path.join(process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)', 'Microsoft Visual Studio', 'Installer', 'vswhere.exe');
            if (fs.existsSync(vsWherePath)) {
                const output = (0, child_process_1.execSync)(`"${vsWherePath}" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64`, {
                    encoding: 'utf8',
                    stdio: 'pipe'
                });
                return output.trim().length > 0;
            }
            // Fallback: check for common Visual Studio paths
            const commonPaths = [
                'C:\\Program Files\\Microsoft Visual Studio\\2022',
                'C:\\Program Files (x86)\\Microsoft Visual Studio\\2019',
                'C:\\Program Files\\Microsoft Visual Studio\\2019',
            ];
            return commonPaths.some(vsPath => fs.existsSync(vsPath));
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Check for Windows SDK installation.
     */
    async checkWindowsSDK() {
        try {
            const sdkPath = 'C:\\Program Files (x86)\\Windows Kits\\10\\bin';
            if (!fs.existsSync(sdkPath)) {
                return false;
            }
            // Check for at least one SDK version
            const versions = fs.readdirSync(sdkPath).filter(dir => dir.match(/^10\.0\.\d+\.\d+$/) && fs.existsSync(path.join(sdkPath, dir)));
            return versions.length > 0;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Check for MSBuild installation.
     */
    async checkMSBuild() {
        try {
            (0, child_process_1.execSync)('msbuild -version', { stdio: 'pipe' });
            return true;
        }
        catch (error) {
            // Try alternative paths
            const msbuildPaths = [
                'C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe',
                'C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\MSBuild.exe',
                'C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe',
                'C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe',
                'C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\MSBuild\\Current\\Bin\\MSBuild.exe',
                'C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe',
            ];
            return msbuildPaths.some(msbuildPath => fs.existsSync(msbuildPath));
        }
    }
}
exports.WindowsPrerequisite = WindowsPrerequisite;
//# sourceMappingURL=WindowsPrerequisite.js.map