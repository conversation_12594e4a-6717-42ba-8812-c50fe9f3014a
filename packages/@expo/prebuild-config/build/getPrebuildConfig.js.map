{"version": 3, "file": "getPrebuildConfig.js", "names": ["_config", "data", "require", "_getAutolinkedPackages", "_withDefaultPlugins", "withExternalPlatformPlugins", "PlatformDiscovery", "loadExternalPlatformIntegration", "platformRegistryModule", "externalPlatformModule", "registryError", "config", "loadExternalPlatforms", "getPrebuildConfigAsync", "projectRoot", "props", "autolinkedModules", "getAutolinkedPackagesAsync", "platforms", "getPrebuildConfig", "bundleIdentifier", "packageName", "exp", "rest", "getConfig", "skipSDKVersionRequirement", "isModdedConfig", "_internal", "withVersionedExpoSDKPlugins", "withLegacyExpoPlugins", "includes", "ios", "withIosExpoPlugins", "android", "package", "withAndroidExpoPlugins"], "sources": ["../src/getPrebuildConfig.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\n\nimport { getAutolinkedPackagesAsync } from './getAutolinkedPackages';\nimport {\n  withAndroidExpoPlugins,\n  withIosExpoPlugins,\n  withLegacyExpoPlugins,\n  withVersionedExpoSDKPlugins,\n} from './plugins/withDefaultPlugins';\n\n// Import external platform integration\nlet withExternalPlatformPlugins: any = null;\nlet PlatformDiscovery: any = null;\n\n// Lazy load external platform integration to avoid circular dependencies\nfunction loadExternalPlatformIntegration() {\n  if (!withExternalPlatformPlugins || !PlatformDiscovery) {\n    try {\n      // Try new platform registry package first\n      const platformRegistryModule = require('@expo/platform-registry');\n      const externalPlatformModule = require('@expo/cli/build/src/prebuild/withExternalPlatformPlugins');\n\n      withExternalPlatformPlugins = externalPlatformModule.withExternalPlatformPlugins;\n      PlatformDiscovery = platformRegistryModule.PlatformDiscovery;\n    } catch (registryError) {\n      try {\n        // Fallback to CLI package for backward compatibility\n        const externalPlatformModule = require('@expo/cli/build/src/prebuild/withExternalPlatformPlugins');\n        const platformRegistryModule = require('@expo/cli/build/src/core/PlatformRegistry');\n\n        withExternalPlatformPlugins = externalPlatformModule.withExternalPlatformPlugins;\n        PlatformDiscovery = platformRegistryModule.PlatformDiscovery;\n      } catch {\n        // External platform integration not available, continue without it\n        withExternalPlatformPlugins = (config: any) => config;\n        PlatformDiscovery = { loadExternalPlatforms: async () => {} };\n      }\n    }\n  }\n  return { withExternalPlatformPlugins, PlatformDiscovery };\n}\n\nexport async function getPrebuildConfigAsync(\n  projectRoot: string,\n  props: {\n    bundleIdentifier?: string;\n    packageName?: string;\n    platforms: ModPlatform[];\n  }\n): Promise<ReturnType<typeof getConfig>> {\n  const autolinkedModules = await getAutolinkedPackagesAsync(projectRoot, props.platforms);\n\n  // Load external platforms before generating config\n  const { PlatformDiscovery } = loadExternalPlatformIntegration();\n  await PlatformDiscovery.loadExternalPlatforms(projectRoot);\n\n  return getPrebuildConfig(projectRoot, {\n    ...props,\n    autolinkedModules,\n  });\n}\n\nfunction getPrebuildConfig(\n  projectRoot: string,\n  {\n    platforms,\n    bundleIdentifier,\n    packageName,\n    autolinkedModules,\n  }: {\n    bundleIdentifier?: string;\n    packageName?: string;\n    platforms: ModPlatform[];\n    autolinkedModules?: string[];\n  }\n) {\n  // let config: ExpoConfig;\n  let { exp: config, ...rest } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n    isModdedConfig: true,\n  });\n\n  if (autolinkedModules) {\n    if (!config._internal) {\n      config._internal = {};\n    }\n    config._internal.autolinkedModules = autolinkedModules;\n  }\n\n  // Add all built-in plugins first because they should take\n  // priority over the unversioned plugins.\n  config = withVersionedExpoSDKPlugins(config);\n  config = withLegacyExpoPlugins(config);\n\n  if (platforms.includes('ios')) {\n    if (!config.ios) config.ios = {};\n    config.ios.bundleIdentifier =\n      bundleIdentifier ?? config.ios.bundleIdentifier ?? `com.placeholder.appid`;\n\n    // Add all built-in plugins\n    config = withIosExpoPlugins(config, {\n      bundleIdentifier: config.ios.bundleIdentifier,\n    });\n  }\n\n  if (platforms.includes('android')) {\n    if (!config.android) config.android = {};\n    config.android.package = packageName ?? config.android.package ?? `com.placeholder.appid`;\n\n    // Add all built-in plugins\n    config = withAndroidExpoPlugins(config, {\n      package: config.android.package,\n    });\n  }\n\n  // Apply external platform config plugins after built-in plugins\n  const { withExternalPlatformPlugins } = loadExternalPlatformIntegration();\n  config = withExternalPlatformPlugins(config);\n\n  return { exp: config, ...rest };\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAE,uBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,sBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,oBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,mBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA;AACA,IAAII,2BAAgC,GAAG,IAAI;AAC3C,IAAIC,iBAAsB,GAAG,IAAI;;AAEjC;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACzC,IAAI,CAACF,2BAA2B,IAAI,CAACC,iBAAiB,EAAE;IACtD,IAAI;MACF;MACA,MAAME,sBAAsB,GAAGN,OAAO,CAAC,yBAAyB,CAAC;MACjE,MAAMO,sBAAsB,GAAGP,OAAO,CAAC,0DAA0D,CAAC;MAElGG,2BAA2B,GAAGI,sBAAsB,CAACJ,2BAA2B;MAChFC,iBAAiB,GAAGE,sBAAsB,CAACF,iBAAiB;IAC9D,CAAC,CAAC,OAAOI,aAAa,EAAE;MACtB,IAAI;QACF;QACA,MAAMD,sBAAsB,GAAGP,OAAO,CAAC,0DAA0D,CAAC;QAClG,MAAMM,sBAAsB,GAAGN,OAAO,CAAC,2CAA2C,CAAC;QAEnFG,2BAA2B,GAAGI,sBAAsB,CAACJ,2BAA2B;QAChFC,iBAAiB,GAAGE,sBAAsB,CAACF,iBAAiB;MAC9D,CAAC,CAAC,MAAM;QACN;QACAD,2BAA2B,GAAIM,MAAW,IAAKA,MAAM;QACrDL,iBAAiB,GAAG;UAAEM,qBAAqB,EAAE,MAAAA,CAAA,KAAY,CAAC;QAAE,CAAC;MAC/D;IACF;EACF;EACA,OAAO;IAAEP,2BAA2B;IAAEC;EAAkB,CAAC;AAC3D;AAEO,eAAeO,sBAAsBA,CAC1CC,WAAmB,EACnBC,KAIC,EACsC;EACvC,MAAMC,iBAAiB,GAAG,MAAM,IAAAC,mDAA0B,EAACH,WAAW,EAAEC,KAAK,CAACG,SAAS,CAAC;;EAExF;EACA,MAAM;IAAEZ;EAAkB,CAAC,GAAGC,+BAA+B,CAAC,CAAC;EAC/D,MAAMD,iBAAiB,CAACM,qBAAqB,CAACE,WAAW,CAAC;EAE1D,OAAOK,iBAAiB,CAACL,WAAW,EAAE;IACpC,GAAGC,KAAK;IACRC;EACF,CAAC,CAAC;AACJ;AAEA,SAASG,iBAAiBA,CACxBL,WAAmB,EACnB;EACEI,SAAS;EACTE,gBAAgB;EAChBC,WAAW;EACXL;AAMF,CAAC,EACD;EACA;EACA,IAAI;IAAEM,GAAG,EAAEX,MAAM;IAAE,GAAGY;EAAK,CAAC,GAAG,IAAAC,mBAAS,EAACV,WAAW,EAAE;IACpDW,yBAAyB,EAAE,IAAI;IAC/BC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,IAAIV,iBAAiB,EAAE;IACrB,IAAI,CAACL,MAAM,CAACgB,SAAS,EAAE;MACrBhB,MAAM,CAACgB,SAAS,GAAG,CAAC,CAAC;IACvB;IACAhB,MAAM,CAACgB,SAAS,CAACX,iBAAiB,GAAGA,iBAAiB;EACxD;;EAEA;EACA;EACAL,MAAM,GAAG,IAAAiB,iDAA2B,EAACjB,MAAM,CAAC;EAC5CA,MAAM,GAAG,IAAAkB,2CAAqB,EAAClB,MAAM,CAAC;EAEtC,IAAIO,SAAS,CAACY,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC7B,IAAI,CAACnB,MAAM,CAACoB,GAAG,EAAEpB,MAAM,CAACoB,GAAG,GAAG,CAAC,CAAC;IAChCpB,MAAM,CAACoB,GAAG,CAACX,gBAAgB,GACzBA,gBAAgB,IAAIT,MAAM,CAACoB,GAAG,CAACX,gBAAgB,IAAI,uBAAuB;;IAE5E;IACAT,MAAM,GAAG,IAAAqB,wCAAkB,EAACrB,MAAM,EAAE;MAClCS,gBAAgB,EAAET,MAAM,CAACoB,GAAG,CAACX;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAIF,SAAS,CAACY,QAAQ,CAAC,SAAS,CAAC,EAAE;IACjC,IAAI,CAACnB,MAAM,CAACsB,OAAO,EAAEtB,MAAM,CAACsB,OAAO,GAAG,CAAC,CAAC;IACxCtB,MAAM,CAACsB,OAAO,CAACC,OAAO,GAAGb,WAAW,IAAIV,MAAM,CAACsB,OAAO,CAACC,OAAO,IAAI,uBAAuB;;IAEzF;IACAvB,MAAM,GAAG,IAAAwB,4CAAsB,EAACxB,MAAM,EAAE;MACtCuB,OAAO,EAAEvB,MAAM,CAACsB,OAAO,CAACC;IAC1B,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM;IAAE7B;EAA4B,CAAC,GAAGE,+BAA+B,CAAC,CAAC;EACzEI,MAAM,GAAGN,2BAA2B,CAACM,MAAM,CAAC;EAE5C,OAAO;IAAEW,GAAG,EAAEX,MAAM;IAAE,GAAGY;EAAK,CAAC;AACjC", "ignoreList": []}