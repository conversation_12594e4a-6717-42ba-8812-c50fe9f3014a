import { ExternalPlatform } from '../types/ExternalPlatform';

/**
 * Simple registry for tracking external platforms.
 * Focuses only on essential platform management for Phase 0.
 */
export class PlatformRegistry {
  private platforms = new Map<string, ExternalPlatform>();

  /**
   * Register an external platform.
   * @param platformData The platform data from expo-platform-* package
   */
  register(platformData: ExternalPlatform): void {
    this.platforms.set(platformData.platform, platformData);

    // Auto-register development tools if available
    this.registerDevelopmentTools(platformData);

    // Auto-register Phase 3 advanced features if available
    this.registerAdvancedFeatures(platformData);
  }

  /**
   * Register development tools for a platform.
   * This is called automatically when a platform is registered.
   * @param platformData The platform data
   */
  private registerDevelopmentTools(platformData: ExternalPlatform): void {
    // Import here to avoid circular dependencies
    try {
      const { devClientExtensionRegistry } = require('./DevClientExtensionRegistry');

      if (platformData.devClientExtensions) {
        devClientExtensionRegistry.registerDevClientExtensions(
          platformData.platform,
          platformData.devClientExtensions
        );
      }

      if (platformData.testUtilities) {
        devClientExtensionRegistry.registerTestUtilities(
          platformData.platform,
          platformData.testUtilities
        );
      }

      if (platformData.permissions) {
        devClientExtensionRegistry.registerPermissions(
          platformData.platform,
          platformData.permissions
        );
      }
    } catch {
      // Silently fail if DevClientExtensionRegistry is not available
      // This allows platforms to work even if Phase 2 features are not loaded
    }
  }

  /**
   * Register advanced features for a platform.
   * This is called automatically when a platform is registered.
   * @param platformData The platform data
   */
  private registerAdvancedFeatures(platformData: ExternalPlatform): void {
    // Register validation rules if available
    if (platformData.validationRules && Array.isArray(platformData.validationRules)) {
      try {
        // Import here to avoid circular dependencies
        const { PlatformConfigValidation } = require('./PlatformConfigValidation');
        PlatformConfigValidation.registerValidationRules(platformData.validationRules);
      } catch {
        // Silently fail if PlatformConfigValidation is not available
        // This allows platforms to work even if Phase 3 features are not loaded
      }
    }
  }

  /**
   * Get all available external platform names.
   * @returns Array of platform names
   */
  getAvailablePlatforms(): string[] {
    return Array.from(this.platforms.keys());
  }

  /**
   * Check if a platform is registered.
   * @param platform The platform identifier
   * @returns True if the platform is registered
   */
  hasPlatform(platform: string): boolean {
    return this.platforms.has(platform);
  }

  /**
   * Get platform data.
   * @param platform The platform identifier
   * @returns Platform data or undefined if not found
   */
  getPlatform(platform: string): ExternalPlatform | undefined {
    return this.platforms.get(platform);
  }

  /**
   * Get all registered platforms.
   * @returns Array of platform data
   */
  getAllPlatforms(): ExternalPlatform[] {
    return Array.from(this.platforms.values());
  }

  /**
   * Clear all registered platforms.
   * This is primarily useful for testing.
   */
  clear(): void {
    this.platforms.clear();
  }

  /**
   * Get platforms that support a specific feature.
   * @param feature The feature to check for
   * @returns Array of platform names that support the feature
   */
  getPlatformsWithFeature(feature: keyof ExternalPlatform): string[] {
    return this.getAllPlatforms()
      .filter(platform => platform[feature] !== undefined)
      .map(platform => platform.platform);
  }

  /**
   * Get platforms that have customization templates.
   * @returns Array of platform names with customization support
   */
  getPlatformsWithCustomization(): string[] {
    return this.getPlatformsWithFeature('customizationTemplates');
  }

  /**
   * Get customization templates for a platform.
   * @param platform The platform identifier
   * @returns Customization templates or undefined if not available
   */
  getCustomizationTemplates(platform: string): ExternalPlatform['customizationTemplates'] {
    const platformData = this.getPlatform(platform);
    return platformData?.customizationTemplates;
  }
}
