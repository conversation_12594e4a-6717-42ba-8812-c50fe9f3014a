{"version": 3, "file": "external-platform-types.js", "names": [], "sources": ["../../src/plugins/external-platform-types.ts"], "sourcesContent": ["/**\n * TypeScript module declaration to extend ModConfig for external platforms.\n * This allows external platforms to add their own mod configurations using declaration merging.\n */\n\nimport { Mod } from '../Plugin.types';\n\n/**\n * External platform mod configuration interface.\n * External platforms can extend this using TypeScript declaration merging.\n * \n * Example:\n * ```typescript\n * declare module '@expo/config-plugins' {\n *   interface ModConfig {\n *     myplatform?: ExternalPlatformModConfig & {\n *       customMod?: Mod<MyCustomType>;\n *     };\n *   }\n * }\n * ```\n */\nexport interface ExternalPlatformModConfig {\n  /** Dangerously make a modification before any other platform mods have been run. */\n  dangerous?: Mod<unknown>;\n  /** Dangerously make a modification after all the other platform mods have been run. */\n  finalized?: Mod<unknown>;\n  /** Platform-specific manifest or configuration file */\n  manifest?: Mod<any>;\n  /** Platform-specific configuration file */\n  config?: Mod<any>;\n  /** Platform-specific project file */\n  project?: Mod<any>;\n  /** Platform-specific build configuration */\n  build?: Mod<any>;\n  /** Platform-specific resources */\n  resources?: Mod<any>;\n  /** Platform-specific assets */\n  assets?: Mod<any>;\n  /** Platform-specific dependencies */\n  dependencies?: Mod<any>;\n  /** Platform-specific permissions */\n  permissions?: Mod<any>;\n  /** Platform-specific entitlements */\n  entitlements?: Mod<any>;\n  /** Platform-specific metadata */\n  metadata?: Mod<any>;\n}\n\n// Note: External platforms should extend ModConfig in their own packages like this:\n//\n// declare module '@expo/config-plugins' {\n//   interface ModConfig {\n//     myplatform?: ExternalPlatformModConfig & {\n//       // Add platform-specific mods here\n//       myCustomMod?: Mod<MyCustomType>;\n//     };\n//   }\n// }\n//\n// This approach ensures that:\n// 1. External platforms can add their own mod types\n// 2. TypeScript will properly type-check platform-specific mods\n// 3. The core ModConfig interface remains clean\n// 4. Multiple external platforms can coexist without conflicts\n"], "mappings": "", "ignoreList": []}