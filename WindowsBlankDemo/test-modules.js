// Test core modules
const testModules = [
  'expo-constants',
  'expo-file-system',
  'expo-linking',
  'expo-status-bar'
];

async function testModule(moduleName) {
  try {
    const module = require(moduleName);
    console.log(`✅ ${moduleName}: Loaded successfully`);
    return true;
  } catch (error) {
    console.log(`❌ ${moduleName}: Failed - ${error.message}`);
    return false;
  }
}

async function testAllModules() {
  console.log('Testing SDK modules...\n');
  
  let passed = 0;
  for (const moduleName of testModules) {
    const success = await testModule(moduleName);
    if (success) passed++;
  }
  
  console.log(`\nResults: ${passed}/${testModules.length} modules loaded`);
}

testAllModules();
