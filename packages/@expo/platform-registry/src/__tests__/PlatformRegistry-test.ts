import { PlatformRegistry } from '../registry/PlatformRegistry';
import { ExternalPlatform } from '../types/ExternalPlatform';

describe('PlatformRegistry', () => {
  let registry: PlatformRegistry;

  beforeEach(() => {
    registry = new PlatformRegistry();
  });

  afterEach(() => {
    registry.clear();
  });

  const createMockPlatform = (platform: string): ExternalPlatform => ({
    platform,
    displayName: `${platform.charAt(0).toUpperCase() + platform.slice(1)} Platform`,
    metroExtensions: [`.${platform}.js`, `.${platform}.ts`],
    configPlugins: [`expo-platform-${platform}/plugin`],
  });

  describe('register', () => {
    it('should register a platform', () => {
      const platform = createMockPlatform('windows');
      registry.register(platform);

      expect(registry.hasPlatform('windows')).toBe(true);
      expect(registry.getPlatform('windows')).toEqual(platform);
    });

    it('should overwrite existing platform with same name', () => {
      const platform1 = createMockPlatform('windows');
      const platform2 = { ...createMockPlatform('windows'), displayName: 'Updated Windows' };

      registry.register(platform1);
      registry.register(platform2);

      expect(registry.getPlatform('windows')?.displayName).toBe('Updated Windows');
    });
  });

  describe('getAvailablePlatforms', () => {
    it('should return empty array when no platforms registered', () => {
      expect(registry.getAvailablePlatforms()).toEqual([]);
    });

    it('should return all registered platform names', () => {
      registry.register(createMockPlatform('windows'));
      registry.register(createMockPlatform('macos'));

      const platforms = registry.getAvailablePlatforms();
      expect(platforms).toContain('windows');
      expect(platforms).toContain('macos');
      expect(platforms).toHaveLength(2);
    });
  });

  describe('hasPlatform', () => {
    it('should return false for unregistered platform', () => {
      expect(registry.hasPlatform('windows')).toBe(false);
    });

    it('should return true for registered platform', () => {
      registry.register(createMockPlatform('windows'));
      expect(registry.hasPlatform('windows')).toBe(true);
    });
  });

  describe('getPlatform', () => {
    it('should return undefined for unregistered platform', () => {
      expect(registry.getPlatform('windows')).toBeUndefined();
    });

    it('should return platform data for registered platform', () => {
      const platform = createMockPlatform('windows');
      registry.register(platform);
      expect(registry.getPlatform('windows')).toEqual(platform);
    });
  });

  describe('getAllPlatforms', () => {
    it('should return empty array when no platforms registered', () => {
      expect(registry.getAllPlatforms()).toEqual([]);
    });

    it('should return all registered platforms', () => {
      const windows = createMockPlatform('windows');
      const macos = createMockPlatform('macos');

      registry.register(windows);
      registry.register(macos);

      const platforms = registry.getAllPlatforms();
      expect(platforms).toContain(windows);
      expect(platforms).toContain(macos);
      expect(platforms).toHaveLength(2);
    });
  });

  describe('clear', () => {
    it('should remove all registered platforms', () => {
      registry.register(createMockPlatform('windows'));
      registry.register(createMockPlatform('macos'));

      expect(registry.getAvailablePlatforms()).toHaveLength(2);

      registry.clear();

      expect(registry.getAvailablePlatforms()).toHaveLength(0);
      expect(registry.hasPlatform('windows')).toBe(false);
      expect(registry.hasPlatform('macos')).toBe(false);
    });
  });

  describe('getPlatformsWithFeature', () => {
    it('should return platforms that have the specified feature', () => {
      const windowsWithTemplates = {
        ...createMockPlatform('windows'),
        customizationTemplates: {
          'metro.config.js': {
            source: 'templates/metro.config.js',
            destination: 'metro.config.js',
          },
        },
      };
      const macosWithoutTemplates = createMockPlatform('macos');

      registry.register(windowsWithTemplates);
      registry.register(macosWithoutTemplates);

      const platformsWithTemplates = registry.getPlatformsWithFeature('customizationTemplates');
      expect(platformsWithTemplates).toEqual(['windows']);
    });

    it('should return empty array when no platforms have the feature', () => {
      registry.register(createMockPlatform('windows'));
      registry.register(createMockPlatform('macos'));

      const platformsWithTemplates = registry.getPlatformsWithFeature('customizationTemplates');
      expect(platformsWithTemplates).toEqual([]);
    });
  });

  describe('getPlatformsWithCustomization', () => {
    it('should return platforms that have customization templates', () => {
      const windowsWithTemplates = {
        ...createMockPlatform('windows'),
        customizationTemplates: {
          'metro.config.js': {
            source: 'templates/metro.config.js',
            destination: 'metro.config.js',
          },
        },
      };
      const macosWithoutTemplates = createMockPlatform('macos');

      registry.register(windowsWithTemplates);
      registry.register(macosWithoutTemplates);

      const platformsWithCustomization = registry.getPlatformsWithCustomization();
      expect(platformsWithCustomization).toEqual(['windows']);
    });
  });

  describe('getCustomizationTemplates', () => {
    it('should return customization templates for platform', () => {
      const templates = {
        'metro.config.js': {
          source: 'templates/metro.config.js',
          destination: 'metro.config.js',
        },
      };
      const windowsWithTemplates = {
        ...createMockPlatform('windows'),
        customizationTemplates: templates,
      };

      registry.register(windowsWithTemplates);

      expect(registry.getCustomizationTemplates('windows')).toEqual(templates);
    });

    it('should return undefined for platform without templates', () => {
      registry.register(createMockPlatform('windows'));
      expect(registry.getCustomizationTemplates('windows')).toBeUndefined();
    });

    it('should return undefined for unregistered platform', () => {
      expect(registry.getCustomizationTemplates('windows')).toBeUndefined();
    });
  });
});
