import arg from 'arg';

import { RunOptions } from '../core/PlatformRegistry';
import { assertWithOptionsArgs } from '../utils/args';

/**
 * Parse command line arguments into RunOptions for external platforms.
 * This provides a standardized way to parse run command arguments that
 * external platforms can use in their runAsync implementations.
 */
export function parseRunOptions(argv: string[]): RunOptions {
  // Define the argument specification that covers common run command options
  // This is based on the patterns used in iOS and Android run commands
  const rawArgsMap: arg.Spec = {
    // Common options across platforms
    '--help': Boolean,
    '--no-build-cache': Boolean,
    '--no-install': Boolean,
    '--no-bundler': Boolean,
    '--port': Number,
    '--binary': String,
    '--variant': String,
    '--configuration': String,
    '--scheme': String,
    '--device': Boolean,
    '--emulator': Boolean,
    '--release': Boolean,

    // Aliases
    '-h': '--help',
    '-p': '--port',
    '-d': '--device',
  };

  const args = assertWithOptionsArgs(rawArgsMap, {
    argv,
    permissive: true, // Allow additional platform-specific flags
  });

  // Parse additional string/boolean arguments that might be platform-specific
  const additionalArgs: { [key: string]: any } = {};

  // Find arguments that weren't handled by the main parser
  const parsedFlags = new Set([
    ...Object.keys(rawArgsMap),
    ...Object.values(rawArgsMap).filter((v) => typeof v === 'string'),
  ]);

  // Parse remaining arguments as key-value pairs or flags
  for (let i = 0; i < argv.length; i++) {
    const arg = argv[i];
    if (arg.startsWith('--')) {
      const flagName = arg;
      const key = arg.slice(2);

      // Skip if this was already parsed by the main parser
      if (parsedFlags.has(flagName)) {
        // Skip the value if this is a string argument
        if (
          rawArgsMap[flagName] === String &&
          i + 1 < argv.length &&
          !argv[i + 1].startsWith('--')
        ) {
          i++; // Skip the value
        }
        continue;
      }

      const nextArg = argv[i + 1];

      if (nextArg && !nextArg.startsWith('--')) {
        // Key-value pair
        additionalArgs[key] = nextArg;
        i++; // Skip the next argument since we consumed it
      } else {
        // Boolean flag
        additionalArgs[key] = true;
      }
    }
  }

  // Build the RunOptions object
  const options: RunOptions = {
    // Device targeting
    device: args['--device'] || additionalArgs.device,

    // Build options
    buildCache: !args['--no-build-cache'],
    install: !args['--no-install'],
    bundler: !args['--no-bundler'],

    // Development server
    port: args['--port'],

    // Build configuration
    variant: args['--variant'] || (args['--release'] ? 'release' : undefined),
    configuration: args['--configuration'] || (args['--release'] ? 'Release' : undefined),
    scheme: args['--scheme'],

    // Binary installation
    binary: args['--binary'],

    // Platform-specific options
    ...additionalArgs,
  };

  // Remove undefined values to keep the options object clean
  Object.keys(options).forEach((key) => {
    if (options[key] === undefined) {
      delete options[key];
    }
  });

  return options;
}

/**
 * Get the project root from parsed arguments.
 * This is a utility function that external platforms can use.
 */
export function getProjectRootFromArgs(argv: string[]): string {
  // The project root is typically the first non-flag argument or current directory
  // We need to filter out flag values as well as flags
  const nonFlagArgs: string[] = [];

  for (let i = 0; i < argv.length; i++) {
    const arg = argv[i];
    if (arg.startsWith('-')) {
      // This is a flag, check if it has a value
      const nextArg = argv[i + 1];
      if (nextArg && !nextArg.startsWith('-')) {
        // Skip the next argument as it's a flag value
        i++;
      }
    } else {
      // This is not a flag or flag value
      nonFlagArgs.push(arg);
    }
  }

  return nonFlagArgs[0] || process.cwd();
}
