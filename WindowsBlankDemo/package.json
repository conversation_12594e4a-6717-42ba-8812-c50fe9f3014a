{"name": "windowsblankdemo", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "file:../../Code/thirdparty/expo/packages/expo", "expo-platform-windows": "file:../../Code/thirdparty/expo/packages/expo-platform-windows", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "^0.77.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}