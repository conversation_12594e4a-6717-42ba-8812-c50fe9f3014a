import { ExportedConfig, Mod } from '../Plugin.types';
import { withGeneratedBaseMods, ForwardedBaseModOptions } from './createBaseMod';
import { PlatformDiscovery, platformRegistry } from '@expo/platform-registry';

/**
 * External platform mod support for config plugins.
 * This enables external platforms to register their own mods and base mods.
 */

/**
 * Generic external platform mod configuration.
 * External platforms can extend this interface using TypeScript declaration merging.
 */
export interface ExternalPlatformModConfig {
  /** Dangerously make a modification before any other platform mods have been run. */
  dangerous?: Mod<unknown>;
  /** Dangerously make a modification after all the other platform mods have been run. */
  finalized?: Mod<unknown>;
  /** Platform-specific manifest or configuration file */
  manifest?: Mod<any>;
  /** Platform-specific configuration file */
  config?: Mod<any>;
  /** Platform-specific project file */
  project?: Mod<any>;
  /** Platform-specific build configuration */
  build?: Mod<any>;
  /** Platform-specific resources */
  resources?: Mod<any>;
  /** Platform-specific assets */
  assets?: Mod<any>;
  /** Platform-specific dependencies */
  dependencies?: Mod<any>;
  /** Platform-specific permissions */
  permissions?: Mod<any>;
  /** Platform-specific entitlements */
  entitlements?: Mod<any>;
  /** Platform-specific metadata */
  metadata?: Mod<any>;
}

/**
 * Plugin to add external platform base mods to the config.
 * This automatically discovers and registers base mods for all available external platforms.
 */
export function withExternalPlatformBaseMods(
  config: ExportedConfig,
  props: ForwardedBaseModOptions = {}
): ExportedConfig {
  // Load external platforms
  PlatformDiscovery.loadExternalPlatformsSync();

  // Get all available external platforms
  const externalPlatforms = platformRegistry.getAllPlatforms();

  // Register base mods for each external platform
  for (const platform of externalPlatforms) {
    config = withExternalPlatformBaseMod(config, {
      ...props,
      platform: platform.platform,
    });
  }

  return config;
}

/**
 * Plugin to add base mods for a specific external platform.
 */
export function withExternalPlatformBaseMod(
  config: ExportedConfig,
  {
    platform,
    ...props
  }: ForwardedBaseModOptions & {
    platform: string;
  }
): ExportedConfig {
  // Get platform data
  const platformData = platformRegistry.getPlatform(platform);
  if (!platformData) {
    console.warn(`External platform "${platform}" not found, skipping base mods`);
    return config;
  }

  // Create default providers for the external platform
  const providers = createExternalPlatformProviders(platform);

  // Cast platform to work with withGeneratedBaseMods
  return withGeneratedBaseMods(config, {
    ...props,
    platform: platform as any, // Allow external platforms
    providers,
  });
}

/**
 * Create default providers for an external platform.
 * These provide basic file access patterns that external platforms can use.
 */
function createExternalPlatformProviders(platform: string) {
  return {
    dangerous: {
      getFilePath: () => '',
      read: async () => ({}),
      write: async () => {},
    },
    finalized: {
      getFilePath: () => '',
      read: async () => ({}),
      write: async () => {},
    },
    manifest: {
      getFilePath: ({ platformProjectRoot }: any) =>
        `${platformProjectRoot}/${platform}-manifest.json`,
      read: async (filePath: string) => {
        try {
          const fs = require('fs');
          return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch {
          return {};
        }
      },
      write: async (filePath: string, data: any) => {
        const fs = require('fs');
        const path = require('path');
        await fs.promises.mkdir(path.dirname(filePath), { recursive: true });
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
      },
    },
    config: {
      getFilePath: ({ platformProjectRoot }: any) =>
        `${platformProjectRoot}/${platform}-config.json`,
      read: async (filePath: string) => {
        try {
          const fs = require('fs');
          return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch {
          return {};
        }
      },
      write: async (filePath: string, data: any) => {
        const fs = require('fs');
        const path = require('path');
        await fs.promises.mkdir(path.dirname(filePath), { recursive: true });
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
      },
    },
  };
}

/**
 * Helper function to check if a platform is an external platform.
 */
export function isExternalPlatform(platform: string): boolean {
  return platformRegistry.hasPlatform(platform);
}

/**
 * Get all available platforms including external ones.
 */
export function getAllAvailablePlatforms(): string[] {
  return ['ios', 'android', 'web', ...platformRegistry.getAvailablePlatforms()];
}
