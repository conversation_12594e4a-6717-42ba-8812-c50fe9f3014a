import { getConfig } from '@expo/config';

import { platformRegistry } from '../../core/PlatformRegistry';
import {
  resolveExternalPlatformDependencies,
  configureExternalPlatformAutolinking,
  hasExternalPlatforms,
} from '../resolveExternalPlatformDependencies';

jest.mock('@expo/config');
jest.mock('../../core/PlatformRegistry');

const mockGetConfig = getConfig as jest.MockedFunction<typeof getConfig>;

describe('resolveExternalPlatformDependencies', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (platformRegistry as any).getPlatform = jest.fn();
  });

  it('should return empty array when no external platforms are configured', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android'] },
    } as any);

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual([]);
  });

  it('should return empty array when external platforms have no dependency resolver', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    (platformRegistry.getPlatform as jest.Mock).mockReturnValue({
      platform: 'windows',
      // No dependencyResolver
    });

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual([]);
  });

  it('should resolve dependencies from external platforms', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    const mockDependencyResolver = {
      resolveDependencies: jest.fn().mockResolvedValue(['react-native-windows']),
    };

    (platformRegistry.getPlatform as jest.Mock).mockReturnValue({
      platform: 'windows',
      dependencyResolver: mockDependencyResolver,
    });

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual(['react-native-windows']);
    expect(mockDependencyResolver.resolveDependencies).toHaveBeenCalledWith(['react'], '50.0.0');
  });

  it('should handle multiple external platforms', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows', 'macos'] },
    } as any);

    const mockWindowsDependencyResolver = {
      resolveDependencies: jest.fn().mockResolvedValue(['react-native-windows']),
    };

    const mockMacosDependencyResolver = {
      resolveDependencies: jest.fn().mockResolvedValue(['react-native-macos']),
    };

    (platformRegistry.getPlatform as jest.Mock)
      .mockReturnValueOnce({
        platform: 'windows',
        dependencyResolver: mockWindowsDependencyResolver,
      })
      .mockReturnValueOnce({
        platform: 'macos',
        dependencyResolver: mockMacosDependencyResolver,
      });

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual(['react-native-windows', 'react-native-macos']);
  });

  it('should remove duplicate dependencies', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows', 'macos'] },
    } as any);

    const mockDependencyResolver = {
      resolveDependencies: jest.fn().mockResolvedValue(['react-native-shared']),
    };

    (platformRegistry.getPlatform as jest.Mock)
      .mockReturnValueOnce({
        platform: 'windows',
        dependencyResolver: mockDependencyResolver,
      })
      .mockReturnValueOnce({
        platform: 'macos',
        dependencyResolver: mockDependencyResolver,
      });

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual(['react-native-shared']);
  });

  it('should handle errors gracefully', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    const mockDependencyResolver = {
      resolveDependencies: jest.fn().mockRejectedValue(new Error('Test error')),
    };

    (platformRegistry.getPlatform as jest.Mock).mockReturnValue({
      platform: 'windows',
      dependencyResolver: mockDependencyResolver,
    });

    const result = await resolveExternalPlatformDependencies('/test', ['react'], '50.0.0');

    expect(result).toEqual([]);
  });
});

describe('configureExternalPlatformAutolinking', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (platformRegistry as any).getPlatform = jest.fn();
  });

  it('should configure autolinking for external platforms', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    const mockDependencyResolver = {
      configureAutolinking: jest.fn().mockResolvedValue(undefined),
    };

    (platformRegistry.getPlatform as jest.Mock).mockReturnValue({
      platform: 'windows',
      dependencyResolver: mockDependencyResolver,
    });

    await configureExternalPlatformAutolinking('/test', ['react']);

    expect(mockDependencyResolver.configureAutolinking).toHaveBeenCalledWith('/test', ['react']);
  });

  it('should handle platforms without autolinking configuration', async () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    (platformRegistry.getPlatform as jest.Mock).mockReturnValue({
      platform: 'windows',
      dependencyResolver: {
        resolveDependencies: jest.fn(),
        // No configureAutolinking
      },
    });

    // Should not throw
    await configureExternalPlatformAutolinking('/test', ['react']);
  });
});

describe('hasExternalPlatforms', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return true when external platforms are configured', () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'windows'] },
    } as any);

    const result = hasExternalPlatforms('/test');

    expect(result).toBe(true);
  });

  it('should return false when only built-in platforms are configured', () => {
    mockGetConfig.mockReturnValue({
      exp: { platforms: ['ios', 'android', 'web'] },
    } as any);

    const result = hasExternalPlatforms('/test');

    expect(result).toBe(false);
  });

  it('should return false when no platforms are configured', () => {
    mockGetConfig.mockReturnValue({
      exp: {},
    } as any);

    const result = hasExternalPlatforms('/test');

    expect(result).toBe(false);
  });

  it('should handle errors gracefully', () => {
    mockGetConfig.mockImplementation(() => {
      throw new Error('Config error');
    });

    const result = hasExternalPlatforms('/test');

    expect(result).toBe(false);
  });
});
