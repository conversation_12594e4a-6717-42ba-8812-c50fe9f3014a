import { getConfig } from '@expo/config';
import { ModPlatform } from '@expo/config-plugins';

import { getAutolinkedPackagesAsync } from './getAutolinkedPackages';
import {
  withAndroidExpoPlugins,
  withIosExpoPlugins,
  withLegacyExpoPlugins,
  withVersionedExpoSDKPlugins,
} from './plugins/withDefaultPlugins';

// Import external platform integration
let withExternalPlatformPlugins: any = null;
let PlatformDiscovery: any = null;

// Lazy load external platform integration to avoid circular dependencies
function loadExternalPlatformIntegration() {
  if (!withExternalPlatformPlugins || !PlatformDiscovery) {
    try {
      // Try new platform registry package first
      const platformRegistryModule = require('@expo/platform-registry');
      const externalPlatformModule = require('@expo/cli/build/src/prebuild/withExternalPlatformPlugins');

      withExternalPlatformPlugins = externalPlatformModule.withExternalPlatformPlugins;
      PlatformDiscovery = platformRegistryModule.PlatformDiscovery;
    } catch (registryError) {
      try {
        // Fallback to CLI package for backward compatibility
        const externalPlatformModule = require('@expo/cli/build/src/prebuild/withExternalPlatformPlugins');
        const platformRegistryModule = require('@expo/cli/build/src/core/PlatformRegistry');

        withExternalPlatformPlugins = externalPlatformModule.withExternalPlatformPlugins;
        PlatformDiscovery = platformRegistryModule.PlatformDiscovery;
      } catch {
        // External platform integration not available, continue without it
        withExternalPlatformPlugins = (config: any) => config;
        PlatformDiscovery = { loadExternalPlatforms: async () => {} };
      }
    }
  }
  return { withExternalPlatformPlugins, PlatformDiscovery };
}

export async function getPrebuildConfigAsync(
  projectRoot: string,
  props: {
    bundleIdentifier?: string;
    packageName?: string;
    platforms: ModPlatform[];
  }
): Promise<ReturnType<typeof getConfig>> {
  const autolinkedModules = await getAutolinkedPackagesAsync(projectRoot, props.platforms);

  // Load external platforms before generating config
  const { PlatformDiscovery } = loadExternalPlatformIntegration();
  await PlatformDiscovery.loadExternalPlatforms(projectRoot);

  return getPrebuildConfig(projectRoot, {
    ...props,
    autolinkedModules,
  });
}

function getPrebuildConfig(
  projectRoot: string,
  {
    platforms,
    bundleIdentifier,
    packageName,
    autolinkedModules,
  }: {
    bundleIdentifier?: string;
    packageName?: string;
    platforms: ModPlatform[];
    autolinkedModules?: string[];
  }
) {
  // let config: ExpoConfig;
  let { exp: config, ...rest } = getConfig(projectRoot, {
    skipSDKVersionRequirement: true,
    isModdedConfig: true,
  });

  if (autolinkedModules) {
    if (!config._internal) {
      config._internal = {};
    }
    config._internal.autolinkedModules = autolinkedModules;
  }

  // Add all built-in plugins first because they should take
  // priority over the unversioned plugins.
  config = withVersionedExpoSDKPlugins(config);
  config = withLegacyExpoPlugins(config);

  if (platforms.includes('ios')) {
    if (!config.ios) config.ios = {};
    config.ios.bundleIdentifier =
      bundleIdentifier ?? config.ios.bundleIdentifier ?? `com.placeholder.appid`;

    // Add all built-in plugins
    config = withIosExpoPlugins(config, {
      bundleIdentifier: config.ios.bundleIdentifier,
    });
  }

  if (platforms.includes('android')) {
    if (!config.android) config.android = {};
    config.android.package = packageName ?? config.android.package ?? `com.placeholder.appid`;

    // Add all built-in plugins
    config = withAndroidExpoPlugins(config, {
      package: config.android.package,
    });
  }

  // Apply external platform config plugins after built-in plugins
  const { withExternalPlatformPlugins } = loadExternalPlatformIntegration();
  config = withExternalPlatformPlugins(config);

  return { exp: config, ...rest };
}
