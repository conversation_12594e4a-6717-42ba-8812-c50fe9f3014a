/**
 * Centralized error messages for external platform integration.
 * This helps maintain consistency across all platform-related error messages.
 */
export class PlatformErrorMessages {
  static platformPackageLoadFailed(packageName: string, error: string): string {
    const platformName = packageName.replace('expo-platform-', '');
    return (
      `⚠️  Failed to load external platform package "${packageName}".\n` +
      `   This package will be ignored. Error: ${error}\n\n` +
      `   Common solutions:\n` +
      `   • Reinstall the package: npm install ${packageName}\n` +
      `   • Check package compatibility with your Expo SDK version\n` +
      `   • Verify the package exports valid platform data\n` +
      `   • Check the package documentation for setup requirements\n\n` +
      `   The ${platformName} platform will not be available until this is resolved.`
    );
  }

  static debugPlatformLoaded(packageName: string): string {
    return `Successfully loaded platform package: ${packageName}`;
  }

  static debugNodeModulesNotFound(): string {
    return 'node_modules directory not found, skipping platform discovery';
  }

  static debugNodeModulesReadFailed(error: string): string {
    return `Failed to read node_modules directory: ${error}`;
  }

  static platformValidationFailed(platform: string, availablePlatforms: string[]): string {
    return (
      `Platform "${platform}" is not supported.\n\n` +
      `Available platforms: ${availablePlatforms.join(', ')}\n\n` +
      `If "${platform}" is an external platform, install it first:\n` +
      `  npm install expo-platform-${platform}`
    );
  }

  static noPlatformsConfigured(): string {
    return (
      'No platforms are configured for customization.\n\n' +
      'Make sure your app.json includes platforms that support Metro bundling.'
    );
  }

  static noExportDirectoriesFound(searchPath: string): string {
    return (
      `No export directories found. Run \`npx expo export\` first.\n\n` +
      `Looked for platform-specific directories in: ${searchPath}`
    );
  }
}
