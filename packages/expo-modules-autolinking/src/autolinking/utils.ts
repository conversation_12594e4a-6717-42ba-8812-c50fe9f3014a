import path from 'path';

import { SupportedPlatform } from '../types';

// Dynamic import to avoid circular dependencies
let platformRegistry: any = null;

/**
 * Get the platform registry instance.
 * This is loaded dynamically to avoid circular dependencies.
 */
function getPlatformRegistry() {
  if (!platformRegistry) {
    try {
      // Try new platform registry package first
      const registryPackage = require('@expo/platform-registry');
      platformRegistry = registryPackage.platformRegistry;
    } catch (registryError) {
      try {
        // Fallback to CLI package for backward compatibility
        const cliPackage = require('@expo/cli/src/core/PlatformRegistry');
        platformRegistry = cliPackage.platformRegistry;
      } catch {
        // If neither package is available, use a mock registry
        platformRegistry = {
          getAvailablePlatforms: () => [],
          getPlatform: () => undefined,
        };
      }
    }
  }
  return platformRegistry;
}

export function getLinkingImplementationForPlatform(platform: SupportedPlatform | string) {
  const registry = getPlatformRegistry();

  // Try to get platform data first
  const platformData = registry.getPlatform(platform);

  if (platformData) {
    // If platform has custom autolinking implementation, use it
    if (platformData.autolinkingImplementation) {
      return platformData.autolinkingImplementation;
    }
  }

  // Fallback to built-in implementations
  return getBuiltInPlatformImplementation(platform);
}

/**
 * Get autolinking implementation for built-in platforms.
 * This is used as a fallback when platform providers are not available.
 */
function getBuiltInPlatformImplementation(platform: SupportedPlatform | string) {
  switch (platform) {
    case 'ios':
    case 'macos':
    case 'tvos':
    case 'apple':
      return require('../platforms/apple');
    case 'android':
      return require('../platforms/android');
    case 'devtools':
      return require('../platforms/devtools');
    default:
      return null;
  }
}

/**
 * Get the possible path to the pnpm isolated modules folder.
 */
export function getIsolatedModulesPath(packagePath: string, packageName: string): string | null {
  // Check if the project is using isolated modules, by checking
  // if the parent dir of `packagePath` is a `node_modules` folder.
  // Isolated modules installs dependencies in small groups such as:
  //   - /.pnpm/expo@50.x.x(...)/node_modules/@expo/cli
  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo
  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo-application
  // When isolated modules are detected, expand the `searchPaths`
  // to include possible nested dependencies.
  const maybeIsolatedModulesPath = path.join(
    packagePath,
    packageName.startsWith('@') && packageName.includes('/') ? '../..' : '..' // scoped packages are nested deeper
  );
  const isIsolatedModulesPath = path.basename(maybeIsolatedModulesPath) === 'node_modules';
  return isIsolatedModulesPath ? maybeIsolatedModulesPath : null;
}
