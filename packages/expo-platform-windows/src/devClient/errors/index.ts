/**
 * Windows Error Boundary Configurations
 *
 * Provides configuration for Windows-specific error boundaries.
 * The actual React components would be implemented by the Expo development client.
 */

export interface WindowsErrorBoundaryConfig {
  type: 'windows' | 'uwp' | 'winappsdk';
  name: string;
  description: string;
  fallbackComponent?: string;
  errorHandler?: string;
  contextProvider?: string;
}

/**
 * Windows Error Boundary Configuration
 */
export const windowsErrorBoundaryConfig: WindowsErrorBoundaryConfig = {
  type: 'windows',
  name: 'Windows Error Boundary',
  description: 'Handles general Windows application errors with Windows-specific context logging',
  fallbackComponent: 'expo-platform-windows/devClient/components/WindowsErrorFallback',
  errorHandler: 'expo-platform-windows/devClient/handlers/windowsErrorHandler',
  contextProvider: 'expo-platform-windows/devClient/context/windowsErrorContext',
};

/**
 * UWP Error Boundary Configuration
 */
export const uwpErrorBoundaryConfig: WindowsErrorBoundaryConfig = {
  type: 'uwp',
  name: 'UWP Error Boundary',
  description: 'Handles UWP-specific errors with package and capability context',
  fallbackComponent: 'expo-platform-windows/devClient/components/UWPErrorFallback',
  errorHandler: 'expo-platform-windows/devClient/handlers/uwpErrorHandler',
  contextProvider: 'expo-platform-windows/devClient/context/uwpErrorContext',
};

/**
 * WinAppSDK Error Boundary Configuration
 */
export const winAppSDKErrorBoundaryConfig: WindowsErrorBoundaryConfig = {
  type: 'winappsdk',
  name: 'WinAppSDK Error Boundary',
  description: 'Handles Windows App SDK errors with framework-specific diagnostics',
  fallbackComponent: 'expo-platform-windows/devClient/components/WinAppSDKErrorFallback',
  errorHandler: 'expo-platform-windows/devClient/handlers/winAppSDKErrorHandler',
  contextProvider: 'expo-platform-windows/devClient/context/winAppSDKErrorContext',
};

/**
 * Get all Windows error boundary configurations
 */
export function getWindowsErrorBoundaryConfigs(): WindowsErrorBoundaryConfig[] {
  return [
    windowsErrorBoundaryConfig,
    uwpErrorBoundaryConfig,
    winAppSDKErrorBoundaryConfig,
  ];
}

/**
 * Get error boundary configuration by type
 */
export function getWindowsErrorBoundaryConfig(type: 'windows' | 'uwp' | 'winappsdk'): WindowsErrorBoundaryConfig {
  switch (type) {
    case 'windows':
      return windowsErrorBoundaryConfig;
    case 'uwp':
      return uwpErrorBoundaryConfig;
    case 'winappsdk':
      return winAppSDKErrorBoundaryConfig;
    default:
      return windowsErrorBoundaryConfig;
  }
}
