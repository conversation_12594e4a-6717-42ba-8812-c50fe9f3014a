{"version": 3, "file": "withExternalPlatformMods.js", "names": ["_createBaseMod", "data", "require", "_platformRegistry", "withExternalPlatformBaseMods", "config", "props", "PlatformDiscovery", "loadExternalPlatformsSync", "externalPlatforms", "platformRegistry", "getAllPlatforms", "platform", "withExternalPlatformBaseMod", "platformData", "getPlatform", "console", "warn", "providers", "createExternalPlatformProviders", "withGeneratedBaseMods", "dangerous", "getFilePath", "read", "write", "finalized", "manifest", "platformProjectRoot", "filePath", "fs", "JSON", "parse", "readFileSync", "path", "promises", "mkdir", "dirname", "recursive", "writeFile", "stringify", "isExternalPlatform", "hasPlatform", "getAllAvailablePlatforms", "getAvailablePlatforms"], "sources": ["../../src/plugins/withExternalPlatformMods.ts"], "sourcesContent": ["import { ExportedConfig, Mod } from '../Plugin.types';\nimport { withGeneratedBaseMods, ForwardedBaseModOptions } from './createBaseMod';\nimport { PlatformDiscovery, platformRegistry } from '@expo/platform-registry';\n\n/**\n * External platform mod support for config plugins.\n * This enables external platforms to register their own mods and base mods.\n */\n\n/**\n * Generic external platform mod configuration.\n * External platforms can extend this interface using TypeScript declaration merging.\n */\nexport interface ExternalPlatformModConfig {\n  /** Dangerously make a modification before any other platform mods have been run. */\n  dangerous?: Mod<unknown>;\n  /** Dangerously make a modification after all the other platform mods have been run. */\n  finalized?: Mod<unknown>;\n  /** Platform-specific manifest or configuration file */\n  manifest?: Mod<any>;\n  /** Platform-specific configuration file */\n  config?: Mod<any>;\n  /** Platform-specific project file */\n  project?: Mod<any>;\n  /** Platform-specific build configuration */\n  build?: Mod<any>;\n  /** Platform-specific resources */\n  resources?: Mod<any>;\n  /** Platform-specific assets */\n  assets?: Mod<any>;\n  /** Platform-specific dependencies */\n  dependencies?: Mod<any>;\n  /** Platform-specific permissions */\n  permissions?: Mod<any>;\n  /** Platform-specific entitlements */\n  entitlements?: Mod<any>;\n  /** Platform-specific metadata */\n  metadata?: Mod<any>;\n}\n\n/**\n * Plugin to add external platform base mods to the config.\n * This automatically discovers and registers base mods for all available external platforms.\n */\nexport function withExternalPlatformBaseMods(\n  config: ExportedConfig,\n  props: ForwardedBaseModOptions = {}\n): ExportedConfig {\n  // Load external platforms\n  PlatformDiscovery.loadExternalPlatformsSync();\n\n  // Get all available external platforms\n  const externalPlatforms = platformRegistry.getAllPlatforms();\n\n  // Register base mods for each external platform\n  for (const platform of externalPlatforms) {\n    config = withExternalPlatformBaseMod(config, {\n      ...props,\n      platform: platform.platform,\n    });\n  }\n\n  return config;\n}\n\n/**\n * Plugin to add base mods for a specific external platform.\n */\nexport function withExternalPlatformBaseMod(\n  config: ExportedConfig,\n  {\n    platform,\n    ...props\n  }: ForwardedBaseModOptions & {\n    platform: string;\n  }\n): ExportedConfig {\n  // Get platform data\n  const platformData = platformRegistry.getPlatform(platform);\n  if (!platformData) {\n    console.warn(`External platform \"${platform}\" not found, skipping base mods`);\n    return config;\n  }\n\n  // Create default providers for the external platform\n  const providers = createExternalPlatformProviders(platform);\n\n  // Cast platform to work with withGeneratedBaseMods\n  return withGeneratedBaseMods(config, {\n    ...props,\n    platform: platform as any, // Allow external platforms\n    providers,\n  });\n}\n\n/**\n * Create default providers for an external platform.\n * These provide basic file access patterns that external platforms can use.\n */\nfunction createExternalPlatformProviders(platform: string) {\n  return {\n    dangerous: {\n      getFilePath: () => '',\n      read: async () => ({}),\n      write: async () => {},\n    },\n    finalized: {\n      getFilePath: () => '',\n      read: async () => ({}),\n      write: async () => {},\n    },\n    manifest: {\n      getFilePath: ({ platformProjectRoot }: any) =>\n        `${platformProjectRoot}/${platform}-manifest.json`,\n      read: async (filePath: string) => {\n        try {\n          const fs = require('fs');\n          return JSON.parse(fs.readFileSync(filePath, 'utf8'));\n        } catch {\n          return {};\n        }\n      },\n      write: async (filePath: string, data: any) => {\n        const fs = require('fs');\n        const path = require('path');\n        await fs.promises.mkdir(path.dirname(filePath), { recursive: true });\n        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));\n      },\n    },\n    config: {\n      getFilePath: ({ platformProjectRoot }: any) =>\n        `${platformProjectRoot}/${platform}-config.json`,\n      read: async (filePath: string) => {\n        try {\n          const fs = require('fs');\n          return JSON.parse(fs.readFileSync(filePath, 'utf8'));\n        } catch {\n          return {};\n        }\n      },\n      write: async (filePath: string, data: any) => {\n        const fs = require('fs');\n        const path = require('path');\n        await fs.promises.mkdir(path.dirname(filePath), { recursive: true });\n        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));\n      },\n    },\n  };\n}\n\n/**\n * Helper function to check if a platform is an external platform.\n */\nexport function isExternalPlatform(platform: string): boolean {\n  return platformRegistry.hasPlatform(platform);\n}\n\n/**\n * Get all available platforms including external ones.\n */\nexport function getAllAvailablePlatforms(): string[] {\n  return ['ios', 'android', 'web', ...platformRegistry.getAvailablePlatforms()];\n}\n"], "mappings": ";;;;;;;;;AACA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,kBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,iBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AA4BA;AACA;AACA;AACA;AACO,SAASG,4BAA4BA,CAC1CC,MAAsB,EACtBC,KAA8B,GAAG,CAAC,CAAC,EACnB;EAChB;EACAC,qCAAiB,CAACC,yBAAyB,CAAC,CAAC;;EAE7C;EACA,MAAMC,iBAAiB,GAAGC,oCAAgB,CAACC,eAAe,CAAC,CAAC;;EAE5D;EACA,KAAK,MAAMC,QAAQ,IAAIH,iBAAiB,EAAE;IACxCJ,MAAM,GAAGQ,2BAA2B,CAACR,MAAM,EAAE;MAC3C,GAAGC,KAAK;MACRM,QAAQ,EAAEA,QAAQ,CAACA;IACrB,CAAC,CAAC;EACJ;EAEA,OAAOP,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAASQ,2BAA2BA,CACzCR,MAAsB,EACtB;EACEO,QAAQ;EACR,GAAGN;AAGL,CAAC,EACe;EAChB;EACA,MAAMQ,YAAY,GAAGJ,oCAAgB,CAACK,WAAW,CAACH,QAAQ,CAAC;EAC3D,IAAI,CAACE,YAAY,EAAE;IACjBE,OAAO,CAACC,IAAI,CAAC,sBAAsBL,QAAQ,iCAAiC,CAAC;IAC7E,OAAOP,MAAM;EACf;;EAEA;EACA,MAAMa,SAAS,GAAGC,+BAA+B,CAACP,QAAQ,CAAC;;EAE3D;EACA,OAAO,IAAAQ,sCAAqB,EAACf,MAAM,EAAE;IACnC,GAAGC,KAAK;IACRM,QAAQ,EAAEA,QAAe;IAAE;IAC3BM;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAACP,QAAgB,EAAE;EACzD,OAAO;IACLS,SAAS,EAAE;MACTC,WAAW,EAAEA,CAAA,KAAM,EAAE;MACrBC,IAAI,EAAE,MAAAA,CAAA,MAAa,CAAC,CAAC,CAAC;MACtBC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC;IACtB,CAAC;IACDC,SAAS,EAAE;MACTH,WAAW,EAAEA,CAAA,KAAM,EAAE;MACrBC,IAAI,EAAE,MAAAA,CAAA,MAAa,CAAC,CAAC,CAAC;MACtBC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC;IACtB,CAAC;IACDE,QAAQ,EAAE;MACRJ,WAAW,EAAEA,CAAC;QAAEK;MAAyB,CAAC,KACxC,GAAGA,mBAAmB,IAAIf,QAAQ,gBAAgB;MACpDW,IAAI,EAAE,MAAOK,QAAgB,IAAK;QAChC,IAAI;UACF,MAAMC,EAAE,GAAG3B,OAAO,CAAC,IAAI,CAAC;UACxB,OAAO4B,IAAI,CAACC,KAAK,CAACF,EAAE,CAACG,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,MAAM;UACN,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MACDJ,KAAK,EAAE,MAAAA,CAAOI,QAAgB,EAAE3B,IAAS,KAAK;QAC5C,MAAM4B,EAAE,GAAG3B,OAAO,CAAC,IAAI,CAAC;QACxB,MAAM+B,IAAI,GAAG/B,OAAO,CAAC,MAAM,CAAC;QAC5B,MAAM2B,EAAE,CAACK,QAAQ,CAACC,KAAK,CAACF,IAAI,CAACG,OAAO,CAACR,QAAQ,CAAC,EAAE;UAAES,SAAS,EAAE;QAAK,CAAC,CAAC;QACpE,MAAMR,EAAE,CAACK,QAAQ,CAACI,SAAS,CAACV,QAAQ,EAAEE,IAAI,CAACS,SAAS,CAACtC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACtE;IACF,CAAC;IACDI,MAAM,EAAE;MACNiB,WAAW,EAAEA,CAAC;QAAEK;MAAyB,CAAC,KACxC,GAAGA,mBAAmB,IAAIf,QAAQ,cAAc;MAClDW,IAAI,EAAE,MAAOK,QAAgB,IAAK;QAChC,IAAI;UACF,MAAMC,EAAE,GAAG3B,OAAO,CAAC,IAAI,CAAC;UACxB,OAAO4B,IAAI,CAACC,KAAK,CAACF,EAAE,CAACG,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,MAAM;UACN,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MACDJ,KAAK,EAAE,MAAAA,CAAOI,QAAgB,EAAE3B,IAAS,KAAK;QAC5C,MAAM4B,EAAE,GAAG3B,OAAO,CAAC,IAAI,CAAC;QACxB,MAAM+B,IAAI,GAAG/B,OAAO,CAAC,MAAM,CAAC;QAC5B,MAAM2B,EAAE,CAACK,QAAQ,CAACC,KAAK,CAACF,IAAI,CAACG,OAAO,CAACR,QAAQ,CAAC,EAAE;UAAES,SAAS,EAAE;QAAK,CAAC,CAAC;QACpE,MAAMR,EAAE,CAACK,QAAQ,CAACI,SAAS,CAACV,QAAQ,EAAEE,IAAI,CAACS,SAAS,CAACtC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACtE;IACF;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASuC,kBAAkBA,CAAC5B,QAAgB,EAAW;EAC5D,OAAOF,oCAAgB,CAAC+B,WAAW,CAAC7B,QAAQ,CAAC;AAC/C;;AAEA;AACA;AACA;AACO,SAAS8B,wBAAwBA,CAAA,EAAa;EACnD,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAGhC,oCAAgB,CAACiC,qBAAqB,CAAC,CAAC,CAAC;AAC/E", "ignoreList": []}