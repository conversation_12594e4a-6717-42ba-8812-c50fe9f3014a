{"version": 3, "file": "ExternalPlatform.d.ts", "sourceRoot": "", "sources": ["../../src/types/ExternalPlatform.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,WAAW,UAAU;IACzB,gFAAgF;IAChF,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;IAC1B,6BAA6B;IAC7B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,0CAA0C;IAC1C,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,0DAA0D;IAC1D,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,6BAA6B;IAC7B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,0DAA0D;IAC1D,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,6DAA6D;IAC7D,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,qDAAqD;IACrD,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,uCAAuC;IACvC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;IAC1B,2CAA2C;IAC3C,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB;AAED;;;GAGG;AACH,MAAM,WAAW,kCAAkC;IACjD;;;;;;OAMG;IACH,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAE/E;;;;;;;OAOG;IACH,oBAAoB,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAC/E;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/D,qBAAqB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;CAC1D;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,CAAC,UAAU,EAAE,MAAM,GAAG;QACpB,SAAS,EAAE,OAAO,CAAC;QACnB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC,OAAO,CAAC,EAAE;QACR,KAAK,EAAE,MAAM,EAAE,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,YAAY,CAAC,EAAE;QACb,SAAS,EAAE,OAAO,CAAC;QACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,KAAK,CAAC,EAAE;QACN,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC9C,YAAY,CAAC,EAAE;QACb,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,EAAE,CAAC;IACJ,eAAe,CAAC,EAAE;QAChB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;KAC7B,CAAC;IACF,UAAU,CAAC,EAAE;QACX,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC;KAC5B,CAAC;IACF,UAAU,CAAC,EAAE;QACX,CAAC,aAAa,EAAE,MAAM,GAAG,MAAM,CAAC;KACjC,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IACtB,gBAAgB,CAAC,EAAE;QACjB,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC;KAC3B,CAAC;IACF,aAAa,CAAC,EAAE;QACd,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,CAAC;KACzB,CAAC;IACF,aAAa,CAAC,EAAE;QACd,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;KAC/B,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC9C,CAAC,cAAc,EAAE,MAAM,GAAG;QACxB,GAAG,EAAE,MAAM,CAAC;QACZ,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,OAAO,CAAC;QAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B,+CAA+C;IAC/C,QAAQ,EAAE,MAAM,CAAC;IACjB,+DAA+D;IAC/D,WAAW,CAAC,EAAE,MAAM,CAAC;IAGrB,uEAAuE;IACvE,wBAAwB,CAAC,EAAE,GAAG,CAAC;IAC/B,kFAAkF;IAClF,0BAA0B,CAAC,EAAE,GAAG,CAAC;IACjC,oDAAoD;IACpD,wBAAwB,CAAC,EAAE,GAAG,CAAC;IAC/B,kEAAkE;IAClE,uBAAuB,CAAC,EAAE,GAAG,CAAC;IAG9B,uFAAuF;IACvF,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAGvE,4EAA4E;IAC5E,kBAAkB,CAAC,EAAE,kCAAkC,CAAC;IAGxD,iDAAiD;IACjD,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;IACzB,wDAAwD;IACxD,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,sCAAsC;IACtC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,+DAA+D;IAC/D,yBAAyB,CAAC,EAAE,yBAAyB,CAAC;IAGtD,4CAA4C;IAC5C,gBAAgB,CAAC,EAAE,sBAAsB,CAAC;IAG1C,+DAA+D;IAC/D,aAAa,CAAC,EAAE,yBAAyB,CAAC;IAG1C,+DAA+D;IAC/D,mBAAmB,CAAC,EAAE,+BAA+B,CAAC;IACtD,+DAA+D;IAC/D,aAAa,CAAC,EAAE,wBAAwB,CAAC;IACzC,iEAAiE;IACjE,WAAW,CAAC,EAAE,+BAA+B,CAAC;IAG9C,6EAA6E;IAC7E,eAAe,CAAC,EAAE,GAAG,CAAC;IACtB,kEAAkE;IAClE,WAAW,CAAC,EAAE,GAAG,CAAC;IAClB,yCAAyC;IACzC,eAAe,CAAC,EAAE,GAAG,EAAE,CAAC;IAGxB,gDAAgD;IAChD,sBAAsB,CAAC,EAAE;QACvB,CAAC,YAAY,EAAE,MAAM,GAAG;YACtB,MAAM,EAAE,MAAM,CAAC;YACf,WAAW,EAAE,MAAM,CAAC;YACpB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;SACzB,CAAC;KACH,CAAC;CACH"}