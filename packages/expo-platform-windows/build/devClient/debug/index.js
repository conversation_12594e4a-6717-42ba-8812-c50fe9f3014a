"use strict";
/**
 * Windows Debug Tools Configuration
 *
 * Provides configuration for Windows-specific debug and inspection tools.
 * The actual UI components would be implemented by the Expo development client.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWindowsDebugToolConfig = exports.getWindowsDebugToolConfigs = exports.performanceCounterConfig = exports.uwpCapabilityInspectorConfig = exports.memoryProfilerConfig = exports.windowsInspectorConfig = void 0;
/**
 * Windows Inspector Configuration
 */
exports.windowsInspectorConfig = {
    name: 'Windows Inspector',
    description: 'Inspect Windows-specific properties, capabilities, and system information',
    category: 'inspection',
    component: 'expo-platform-windows/devClient/components/WindowsInspector',
    handler: 'expo-platform-windows/devClient/handlers/windowsInspectorHandler',
    icon: 'search',
    shortcut: 'Ctrl+Shift+I',
};
/**
 * Memory Profiler Configuration
 */
exports.memoryProfilerConfig = {
    name: 'Memory Profiler',
    description: 'Monitor Windows memory usage and performance metrics',
    category: 'profiling',
    component: 'expo-platform-windows/devClient/components/MemoryProfiler',
    handler: 'expo-platform-windows/devClient/handlers/memoryProfilerHandler',
    icon: 'memory',
    shortcut: 'Ctrl+Shift+M',
};
/**
 * UWP Capability Inspector Configuration
 */
exports.uwpCapabilityInspectorConfig = {
    name: 'UWP Capability Inspector',
    description: 'View and test UWP capabilities and permissions',
    category: 'inspection',
    component: 'expo-platform-windows/devClient/components/UWPCapabilityInspector',
    handler: 'expo-platform-windows/devClient/handlers/uwpCapabilityInspectorHandler',
    icon: 'shield',
    shortcut: 'Ctrl+Shift+U',
};
/**
 * Performance Counter Configuration
 */
exports.performanceCounterConfig = {
    name: 'Windows Performance Counter',
    description: 'Monitor Windows performance counters and system metrics',
    category: 'performance',
    component: 'expo-platform-windows/devClient/components/PerformanceCounter',
    handler: 'expo-platform-windows/devClient/handlers/performanceCounterHandler',
    icon: 'chart',
    shortcut: 'Ctrl+Shift+P',
};
/**
 * Get all Windows debug tool configurations
 */
function getWindowsDebugToolConfigs() {
    return [
        exports.windowsInspectorConfig,
        exports.memoryProfilerConfig,
        exports.uwpCapabilityInspectorConfig,
        exports.performanceCounterConfig,
    ];
}
exports.getWindowsDebugToolConfigs = getWindowsDebugToolConfigs;
/**
 * Get debug tool configuration by name
 */
function getWindowsDebugToolConfig(name) {
    return getWindowsDebugToolConfigs().find(tool => tool.name === name);
}
exports.getWindowsDebugToolConfig = getWindowsDebugToolConfig;
//# sourceMappingURL=index.js.map