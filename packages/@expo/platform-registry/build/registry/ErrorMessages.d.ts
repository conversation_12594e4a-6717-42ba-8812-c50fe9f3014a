/**
 * Centralized error messages for external platform integration.
 * This helps maintain consistency across all platform-related error messages.
 */
export declare class PlatformErrorMessages {
    static platformPackageLoadFailed(packageName: string, error: string): string;
    static debugPlatformLoaded(packageName: string): string;
    static debugNodeModulesNotFound(): string;
    static debugNodeModulesReadFailed(error: string): string;
    static platformValidationFailed(platform: string, availablePlatforms: string[]): string;
    static noPlatformsConfigured(): string;
    static noExportDirectoriesFound(searchPath: string): string;
}
//# sourceMappingURL=ErrorMessages.d.ts.map