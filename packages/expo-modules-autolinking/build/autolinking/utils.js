"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIsolatedModulesPath = exports.getLinkingImplementationForPlatform = void 0;
const path_1 = __importDefault(require("path"));
// Dynamic import to avoid circular dependencies
let platformRegistry = null;
/**
 * Get the platform registry instance.
 * This is loaded dynamically to avoid circular dependencies.
 */
function getPlatformRegistry() {
    if (!platformRegistry) {
        try {
            // Try new platform registry package first
            const registryPackage = require('@expo/platform-registry');
            platformRegistry = registryPackage.platformRegistry;
        }
        catch (registryError) {
            try {
                // Fallback to CLI package for backward compatibility
                const cliPackage = require('@expo/cli/src/core/PlatformRegistry');
                platformRegistry = cliPackage.platformRegistry;
            }
            catch {
                // If neither package is available, use a mock registry
                platformRegistry = {
                    getAvailablePlatforms: () => [],
                    getPlatform: () => undefined,
                };
            }
        }
    }
    return platformRegistry;
}
function getLinkingImplementationForPlatform(platform) {
    const registry = getPlatformRegistry();
    // Try to get platform data first
    const platformData = registry.getPlatform(platform);
    if (platformData) {
        // If platform has custom autolinking implementation, use it
        if (platformData.autolinkingImplementation) {
            return platformData.autolinkingImplementation;
        }
    }
    // Fallback to built-in implementations
    return getBuiltInPlatformImplementation(platform);
}
exports.getLinkingImplementationForPlatform = getLinkingImplementationForPlatform;
/**
 * Get autolinking implementation for built-in platforms.
 * This is used as a fallback when platform providers are not available.
 */
function getBuiltInPlatformImplementation(platform) {
    switch (platform) {
        case 'ios':
        case 'macos':
        case 'tvos':
        case 'apple':
            return require('../platforms/apple');
        case 'android':
            return require('../platforms/android');
        case 'devtools':
            return require('../platforms/devtools');
        default:
            return null;
    }
}
/**
 * Get the possible path to the pnpm isolated modules folder.
 */
function getIsolatedModulesPath(packagePath, packageName) {
    // Check if the project is using isolated modules, by checking
    // if the parent dir of `packagePath` is a `node_modules` folder.
    // Isolated modules installs dependencies in small groups such as:
    //   - /.pnpm/expo@50.x.x(...)/node_modules/@expo/cli
    //   - /.pnpm/expo@50.x.x(...)/node_modules/expo
    //   - /.pnpm/expo@50.x.x(...)/node_modules/expo-application
    // When isolated modules are detected, expand the `searchPaths`
    // to include possible nested dependencies.
    const maybeIsolatedModulesPath = path_1.default.join(packagePath, packageName.startsWith('@') && packageName.includes('/') ? '../..' : '..' // scoped packages are nested deeper
    );
    const isIsolatedModulesPath = path_1.default.basename(maybeIsolatedModulesPath) === 'node_modules';
    return isIsolatedModulesPath ? maybeIsolatedModulesPath : null;
}
exports.getIsolatedModulesPath = getIsolatedModulesPath;
//# sourceMappingURL=utils.js.map