/**
 * Windows Platform Prerequisites
 *
 * Provides health checking and prerequisite validation for Windows development.
 * This implementation checks for required tools, SDKs, and development environment setup.
 */
import { ExternalPlatformPrerequisite } from '@expo/cli/src/core/PlatformRegistry';
/**
 * Windows platform prerequisite checker.
 * Validates the Windows development environment including Visual Studio,
 * Windows SDK, and react-native-windows dependencies.
 */
export declare class WindowsPrerequisite extends ExternalPlatformPrerequisite {
    constructor(platform: string);
    /**
     * Check if the development environment is properly set up for Windows development.
     */
    checkDevelopmentEnvironment(): Promise<boolean>;
    /**
     * Check if system requirements are met for Windows development.
     */
    checkSystemRequirements(): Promise<boolean>;
    /**
     * Check if platform-specific tools are installed and available.
     */
    checkPlatformTools(): Promise<boolean>;
    /**
     * Get installation instructions for missing prerequisites.
     */
    getInstallationInstructions(): string[];
    /**
     * Get Windows version information.
     */
    private getWindowsVersion;
    /**
     * Check if the Windows version is supported.
     */
    private isWindowsVersionSupported;
    /**
     * Check for Visual Studio installation.
     */
    private checkVisualStudio;
    /**
     * Check for Windows SDK installation.
     */
    private checkWindowsSDK;
    /**
     * Check for MSBuild installation.
     */
    private checkMSBuild;
}
//# sourceMappingURL=WindowsPrerequisite.d.ts.map