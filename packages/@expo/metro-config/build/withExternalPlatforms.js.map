{"version": 3, "file": "withExternalPlatforms.js", "sourceRoot": "", "sources": ["../src/withExternalPlatforms.ts"], "names": [], "mappings": ";;;AAEA;;;;;;;GAOG;AACH,SAAgB,qBAAqB,CAAC,MAAe,EAAE,YAAkB;IACvE,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QAC1B,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;KACtD;IAED,8CAA8C;IAC9C,IAAI,YAAY,EAAE;QAChB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACtD;QACD,OAAO,kCAAkC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;KACjE;IAED,IAAI;QACF,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,sBAAsB,CAAC;QAC3B,IAAI;YACF,0CAA0C;YAC1C,sBAAsB,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAC5D,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;aAC/E;SACF;QAAC,OAAO,aAAkB,EAAE;YAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;aAChF;YACD,IAAI;gBACF,0CAA0C;gBAC1C,sBAAsB,GAAG,OAAO,CAAC,2CAA2C,CAAC,CAAC;gBAC9E,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;iBACtE;aACF;YAAC,OAAO,UAAe,EAAE;gBACxB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;iBACpE;gBACD,IAAI;oBACF,4CAA4C;oBAC5C,sBAAsB,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;oBACxE,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;wBAC1B,OAAO,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;qBACvE;iBACF;gBAAC,OAAO,WAAgB,EAAE;oBACzB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;wBAC1B,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;qBACtE;oBACD,MAAM,WAAW,CAAC;iBACnB;aACF;SACF;QAED,IAAI,sBAAsB,EAAE,gBAAgB,IAAI,sBAAsB,EAAE,iBAAiB,EAAE;YACzF,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;aACjF;YAED,2CAA2C;YAC3C,+EAA+E;YAC/E,IAAI;gBACF,gEAAgE;gBAChE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;gBAClC,sBAAsB,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAEhF,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;iBAC3E;aACF;YAAC,OAAO,SAAc,EAAE;gBACvB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;iBAC/E;aACF;YAED,OAAO,kCAAkC,CAAC,MAAM,EAAE,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;SAC5F;aAAM;YACL,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC1B,OAAO,CAAC,KAAK,CAAC,4FAA4F,CAAC,CAAC;aAC7G;SACF;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,4FAA4F;QAC5F,6EAA6E;QAC7E,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YACpE,OAAO,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;YACvF,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;SACjE;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAzFD,sDAyFC;AAED;;;GAGG;AACH,SAAS,wBAAwB,CAAC,SAAmB;IACnD,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAElD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;YAChC,gBAAgB,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC,CAAC;SAC9C;KACF;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;GAIG;AACH,SAAS,kCAAkC,CAAC,MAAe,EAAE,gBAAqB;IAChF,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;IAEnE,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QAC1B,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,iBAAiB,CAAC,CAAC;KAC9E;IAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC;SACpF;QACD,OAAO,MAAM,CAAC;KACf;IAED,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1E,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,EAAE,UAAU,IAAI,EAAE,CAAC;IAE5D,0EAA0E;IAC1E,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE,CACvE,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACpC,CAAC;IAEF,IAAI,oBAAoB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QAClD,OAAO,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;QAC3F,OAAO,MAAM,CAAC;KACf;IAED,iEAAiE;IACjE,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,gBAAgB,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAE/E,uDAAuD;IACvD,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE7C,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE;QACxC,MAAM,YAAY,GAAG,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,YAAY,EAAE,eAAe,EAAE;YACjC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,EAAE;gBACnD,4BAA4B;gBAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBAClD,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAC7B;qBAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;oBACjC,OAAO,CAAC,IAAI,CACV,6BAA6B,GAAG,mBAAmB,QAAQ,uBAAuB,CACnF,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;SACJ;KACF;IAED,uEAAuE;IACvE,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9F,6DAA6D;IAC7D,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IACrE,MAAM,mBAAmB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAElF,kDAAkD;IAClD,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAC3C,CAAC,QAAgB,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAC3D,CAAC;IACF,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CACzD,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAClD,CAAC;IAEF,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QACrD,OAAO,CAAC,GAAG,CAAC,oCAAoC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,qCAAqC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC9E;QACD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,mCAAmC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC/E;KACF;IAED,OAAO;QACL,GAAG,MAAM;QACT,QAAQ,EAAE;YACR,GAAG,MAAM,CAAC,QAAQ;YAClB,SAAS,EAAE,YAAY;YACvB,UAAU,EAAE,mBAAmB;SAChC;KACF,CAAC;AACJ,CAAC"}