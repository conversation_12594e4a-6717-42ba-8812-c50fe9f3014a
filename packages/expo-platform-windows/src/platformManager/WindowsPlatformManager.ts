/**
 * Windows Platform Manager Implementation
 *
 * Provides platform management capabilities for Windows including:
 * - Building Windows applications (UWP and WinAppSDK)
 * - Deploying to desktop and remote devices
 * - Managing development workflow
 * - Supporting both old and new React Native architectures
 *
 * This implementation provides a foundation for Windows development workflow.
 */

import { DeviceManager } from '@expo/cli/build/src/start/platforms/DeviceManager';
import {
  PlatformManager,
  BaseOpenInCustomProps,
  BaseResolveDeviceProps,
} from '@expo/cli/build/src/start/platforms/PlatformManager';
import * as build from '@react-native-windows/cli/lib-commonjs/utils/build';
import * as deploy from '@react-native-windows/cli/lib-commonjs/utils/deploy';
import MSBuildTools from '@react-native-windows/cli/lib-commonjs/utils/msbuildtools';
import Version from '@react-native-windows/cli/lib-commonjs/utils/version';
import { exec, spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

// Import Expo CLI base classes for proper integration

import { WindowsDeviceManager, WindowsDevice } from '../deviceManager/WindowsDeviceManager';
import { WindowsAutolinking } from '../autolinking/WindowsAutolinking';

const execAsync = promisify(exec);

// Simple platform options interface
export interface PlatformOpenOptions {
  projectRoot: string;
}

export interface WindowsPlatformOpenOptions extends PlatformOpenOptions {
  // Windows-specific options
  arch?: 'x86' | 'x64' | 'ARM64';
  configuration?: 'Debug' | 'Release' | 'DebugBundle' | 'ReleaseBundle';
  device?: boolean;
  emulator?: boolean;
  target?: string;
  sln?: string;
  proj?: string;
  msbuildprops?: string;
  buildLogDirectory?: string;
  singleproc?: boolean;
  deployFromLayout?: boolean;
  remoteDebugging?: boolean;
  directDebugging?: boolean;
  autolink?: boolean;
  build?: boolean;
  deploy?: boolean;
  launch?: boolean;
  packager?: boolean;
  bundle?: boolean;
}

/**
 * Windows Platform Manager that handles Windows development workflow
 * including building, deploying, and launching Windows applications.
 *
 * Extends Expo CLI's PlatformManager base class for proper integration.
 */
export class WindowsPlatformManager extends PlatformManager<
  WindowsDevice,
  WindowsPlatformOpenOptions & BaseOpenInCustomProps
> {
  constructor(
    projectRoot: string,
    options: {
      getDevServerUrl: () => string | null;
      getExpoGoUrl: () => string;
      getRedirectUrl: () => string | null;
      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;
      resolveDeviceAsync: (
        options?: Partial<BaseResolveDeviceProps<WindowsDevice>>
      ) => Promise<DeviceManager<WindowsDevice>>;
    }
  ) {
    super(projectRoot, {
      platform: 'windows' as any, // Cast to satisfy type constraint
      ...options,
    });
  }
  /**
   * Open/run a Windows application with the specified options (required by PlatformManager base class)
   */
  async openAsync(
    options:
      | { runtime: 'expo' | 'web' }
      | { runtime: 'custom'; props?: Partial<WindowsPlatformOpenOptions & BaseOpenInCustomProps> },
    resolveSettings?: Partial<BaseResolveDeviceProps<WindowsDevice>>
  ): Promise<{ url: string }> {
    if (options.runtime === 'custom') {
      // Handle custom runtime (Windows native app)
      const windowsOptions: WindowsPlatformOpenOptions = {
        projectRoot: this.projectRoot,
        ...options.props,
      };

      await this.openWindowsApp(windowsOptions);

      // Return a placeholder URL for Windows apps
      return { url: 'windows://localhost' };
    } else {
      // For expo/web runtime, delegate to base class
      return super.openAsync(options, resolveSettings);
    }
  }

  /**
   * Internal method to handle Windows-specific app opening
   */
  private async openWindowsApp(options: WindowsPlatformOpenOptions): Promise<void> {
    const { projectRoot } = options;

    // Validate Windows development environment
    await this.validateEnvironment();

    // Detect architecture (old vs new) if not specified
    const isNewArch = await this.detectNewArchitecture(projectRoot);

    console.log(`🔧 Building Windows app (${isNewArch ? 'New' : 'Old'} Architecture)...`);

    if (isNewArch) {
      await this.openNewArchitecture(options);
    } else {
      await this.openOldArchitecture(options);
    }
  }

  /**
   * Get default run options for Windows platform
   */
  getDefaultRunOptions(): WindowsPlatformOpenOptions {
    return {
      projectRoot: process.cwd(),
      arch: 'x64',
      configuration: 'Debug',
      autolink: true,
      build: true,
      deploy: true,
      launch: true,
      packager: true,
      bundle: false,
    };
  }

  /**
   * Validate Windows development environment
   */
  async validateEnvironment(): Promise<{ isValid: boolean; issues: string[]; warnings: string[] }> {
    const issues: string[] = [];
    const warnings: string[] = [];

    console.log('🔍 Validating Windows development environment...');

    // Check for MSBuild using react-native-windows infrastructure
    try {
      const buildTools = MSBuildTools.findAvailableVersion('x64' as any, false);
      if (!buildTools) {
        issues.push('MSBuild not found. Install Visual Studio 2019 or later with C++ build tools.');
      } else {
        console.log(`✅ MSBuild found: ${buildTools.installationVersion}`);
      }
    } catch (error) {
      issues.push(`MSBuild validation failed: ${error}`);
    }

    // Check for Windows SDK using react-native-windows infrastructure
    try {
      const uapVersions = MSBuildTools.getAllAvailableUAPVersions();
      if (uapVersions.length === 0) {
        warnings.push('Windows 10/11 SDK not found. Install Windows SDK for UWP development.');
      } else {
        const latestVersion = uapVersions.sort((a, b) => Version.compare(b, a))[0];
        console.log(`✅ Windows SDK found: ${latestVersion.toString()}`);

        // Check for minimum required version
        const minVersion = new Version(10, 0, 22621, 0);
        if (!latestVersion.gte(minVersion)) {
          warnings.push(`Windows SDK version ${latestVersion.toString()} found, but version 10.0.22621.0 or later is recommended.`);
        }
      }
    } catch (error) {
      warnings.push(`Windows SDK validation failed: ${error}`);
    }

    // Check for Node.js version
    try {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      if (majorVersion < 18) {
        warnings.push(`Node.js ${nodeVersion} found, but version 18 or later is recommended.`);
      } else {
        console.log(`✅ Node.js version: ${nodeVersion}`);
      }
    } catch (error) {
      warnings.push('Node.js version check failed');
    }

    // Check for react-native-windows CLI
    try {
      await execAsync('npx react-native --version', { timeout: 10000 });
      console.log('✅ React Native CLI available');
    } catch (error) {
      warnings.push('React Native CLI not found. Install with: npm install -g @react-native-community/cli');
    }

    // Check for Windows development features
    try {
      // Check if developer mode is enabled
      const devModeResult = await execAsync('reg query "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AppModelUnlock" /v AllowDevelopmentWithoutDevLicense', { timeout: 5000 });
      if (!devModeResult.stdout.includes('0x1')) {
        warnings.push('Windows Developer Mode may not be enabled. Enable it in Settings > Update & Security > For developers.');
      } else {
        console.log('✅ Windows Developer Mode enabled');
      }
    } catch (error) {
      warnings.push('Could not verify Windows Developer Mode status');
    }

    const isValid = issues.length === 0;

    if (isValid) {
      console.log('✅ Windows development environment validation passed');
    } else {
      console.log('❌ Windows development environment validation failed');
      issues.forEach(issue => console.log(`  ❌ ${issue}`));
    }

    if (warnings.length > 0) {
      console.log('⚠️  Environment warnings:');
      warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
    }

    return {
      isValid,
      issues,
      warnings,
    };
  }

  /**
   * Handle New Architecture (C++ only) workflow
   */
  private async openNewArchitecture(options: WindowsPlatformOpenOptions): Promise<void> {
    const { projectRoot } = options;

    // Find New Architecture solution file
    const slnFile = this.findSolutionFile(projectRoot, options.sln);
    if (!slnFile) {
      throw new Error('Windows solution file not found. Run "expo prebuild" first.');
    }

    console.log('🔧 Using New Architecture (C++ only)');

    // Run autolinking if enabled
    if (options.autolink !== false) {
      console.log('🔗 Running autolinking...');
      await this.runAutolinking(projectRoot, options);
    }

    // Build if requested
    if (options.build !== false) {
      console.log('🔨 Building Windows application...');
      await this.buildSolution(slnFile, options);
    }

    // Deploy if requested
    if (options.deploy !== false) {
      console.log('🚀 Deploying Windows application...');
      await this.deployApplication(projectRoot, options);
    }

    // Start packager if requested
    if (this.shouldLaunchPackager(options)) {
      console.log('📦 Starting Metro packager...');
      await this.startPackager(projectRoot);
    }
  }

  /**
   * Handle Old Architecture (C++ + C#) workflow
   */
  private async openOldArchitecture(options: WindowsPlatformOpenOptions): Promise<void> {
    const { projectRoot } = options;

    // Find Old Architecture solution file
    const slnFile = this.findSolutionFile(projectRoot, options.sln);
    if (!slnFile) {
      throw new Error('Windows solution file not found. Run "expo prebuild" first.');
    }

    console.log('🔧 Using Old Architecture (C++ + C#)');

    // Run autolinking if enabled
    if (options.autolink !== false) {
      console.log('🔗 Running autolinking...');
      await this.runAutolinking(projectRoot, options);
    }

    // Build if requested
    if (options.build !== false) {
      console.log('🔨 Building Windows application...');
      await this.buildSolution(slnFile, options);
    }

    // Deploy if requested
    if (options.deploy !== false) {
      console.log('🚀 Deploying Windows application...');
      await this.deployApplication(projectRoot, options);
    }

    // Start packager if requested
    if (this.shouldLaunchPackager(options)) {
      console.log('📦 Starting Metro packager...');
      await this.startPackager(projectRoot);
    }
  }

  /**
   * Find Windows solution file
   */
  private findSolutionFile(projectRoot: string, customSln?: string): string | null {
    if (customSln) {
      const customPath = path.resolve(projectRoot, customSln);
      if (fs.existsSync(customPath)) {
        return customPath;
      }
    }

    const windowsDir = path.join(projectRoot, 'windows');
    if (!fs.existsSync(windowsDir)) {
      return null;
    }

    // Look for .sln files in windows directory
    const files = fs.readdirSync(windowsDir);
    const slnFile = files.find((file) => file.endsWith('.sln'));

    return slnFile ? path.join(windowsDir, slnFile) : null;
  }

  /**
   * Build Windows solution using react-native-windows infrastructure
   */
  private async buildSolution(slnFile: string, options: WindowsPlatformOpenOptions): Promise<void> {
    const configuration = options.configuration || 'Debug';
    const arch = options.arch || 'x64';

    try {
      // Get MSBuild tools using react-native-windows infrastructure
      const buildTools = MSBuildTools.findAvailableVersion(arch as any, false);
      if (!buildTools) {
        throw new Error(`No MSBuild tools found for architecture: ${arch}`);
      }

      // Parse MSBuild properties
      const msBuildProps: Record<string, string> = {};
      if (options.msbuildprops) {
        const props = options.msbuildprops.split(';');
        props.forEach((prop) => {
          const [key, value] = prop.split('=');
          if (key && value) {
            msBuildProps[key.trim()] = value.trim();
          }
        });
      }

      // Build using react-native-windows infrastructure
      await build.buildSolution(
        buildTools,
        slnFile,
        configuration as any,
        arch as any,
        msBuildProps,
        false, // verbose
        'build', // target
        undefined, // buildLogDirectory
        false // singleproc
      );

      console.log('Windows build completed successfully');
    } catch (error) {
      console.error('Windows build failed:', error);
      throw error;
    }
  }

  /**
   * Run autolinking for Windows
   */
  private async runAutolinking(
    projectRoot: string,
    options: WindowsPlatformOpenOptions
  ): Promise<void> {
    try {
      console.log('🔗 Running Windows autolinking...');

      // Create autolinking instance
      const autolinking = new WindowsAutolinking();

      // Generate autolinking files
      const result = await autolinking.generatePackageListAsync(projectRoot);

      console.log(`✅ ${result}`);

      // Also run react-native-windows autolinking if available
      try {
        await this.runReactNativeWindowsAutolinking(projectRoot);
      } catch (error) {
        console.warn('⚠️  react-native-windows autolinking not available, using expo-platform-windows implementation only');
      }

    } catch (error) {
      console.error('❌ Windows autolinking failed:', error);
      throw error;
    }
  }

  /**
   * Run react-native-windows autolinking command if available
   */
  private async runReactNativeWindowsAutolinking(projectRoot: string): Promise<void> {
    try {
      // Try to run react-native-windows autolinking
      const result = await execAsync('npx react-native autolink-windows', {
        cwd: projectRoot,
        timeout: 30000, // 30 second timeout
      });

      if (result.stdout) {
        console.log('📦 react-native-windows autolinking output:', result.stdout);
      }
    } catch (error) {
      // This is expected if react-native-windows CLI is not available
      throw new Error('react-native-windows autolinking not available');
    }
  }

  /**
   * Deploy Windows application using react-native-windows infrastructure
   */
  private async deployApplication(
    projectRoot: string,
    options: WindowsPlatformOpenOptions
  ): Promise<void> {
    try {
      // Create a mock config object for react-native-windows deployment
      const config = {
        project: {
          windows: {
            sourceDir: 'windows',
            solutionFile: this.findSolutionFile(projectRoot)?.split(path.sep).pop() || '',
            project: {
              projectName: path.basename(projectRoot),
              projectFile: '',
            },
          },
        },
      };

      // Create deployment options compatible with react-native-windows
      const deployOptions = {
        ...options,
        root: projectRoot,
        arch: options.arch || 'x64',
        release: options.configuration === 'Release',
        deploy: true,
      };

      if (options.device || options.emulator || options.target) {
        // Deploy to remote device using react-native-windows infrastructure
        await deploy.deployToDevice(deployOptions as any, false, config as any);
      } else {
        // Deploy to desktop using react-native-windows infrastructure
        const buildTools = MSBuildTools.findAvailableVersion(deployOptions.arch as any, false);
        if (!buildTools) {
          throw new Error(`No MSBuild tools found for architecture: ${deployOptions.arch}`);
        }

        await deploy.deployToDesktop(deployOptions as any, false, config as any, buildTools);
      }

      console.log('Windows deployment completed successfully');
    } catch (error) {
      console.error('Windows deployment failed:', error);
      throw error;
    }
  }

  /**
   * Start Metro packager
   */
  private async startPackager(projectRoot: string): Promise<void> {
    console.log('🚀 Starting Metro packager...');
    try {
      // Start Metro packager using npx
      const metroProcess = spawn('npx', ['expo', 'start', '--dev-client'], {
        cwd: projectRoot,
        stdio: 'inherit',
        detached: false,
      });

      metroProcess.on('error', (error) => {
        console.error('Failed to start Metro packager:', error);
      });

      console.log('✅ Metro packager started');
    } catch (error) {
      console.error('Failed to start Metro packager:', error);
      throw error;
    }
  }

  /**
   * Detect if project uses New Architecture
   */
  private async detectNewArchitecture(projectRoot: string): Promise<boolean> {
    const experimentalFeaturesPath = path.join(
      projectRoot,
      'windows',
      'ExperimentalFeatures.props'
    );

    if (fs.existsSync(experimentalFeaturesPath)) {
      const content = fs.readFileSync(experimentalFeaturesPath, 'utf8');
      return content.includes('<RnwNewArch>true</RnwNewArch>');
    }

    return false;
  }

  /**
   * Check if should launch Metro packager
   */
  private shouldLaunchPackager(options: WindowsPlatformOpenOptions): boolean {
    return options.packager === true && options.launch !== false && options.bundle !== true;
  }
}
