{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "build", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src"], "exclude": ["**/__tests__", "**/__mocks__", "build"]}