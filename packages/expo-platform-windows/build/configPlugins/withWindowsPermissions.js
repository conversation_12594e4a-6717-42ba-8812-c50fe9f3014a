"use strict";
/**
 * Windows Permissions Config Plugin
 *
 * Handles Windows-specific permission configuration including:
 * - UWP capability declarations
 * - Device capability requirements
 * - Privacy policy requirements
 * - Permission request handling
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withWindowsPermissions = void 0;
const config_plugins_1 = require("@expo/config-plugins");
const fast_xml_parser_1 = require("fast-xml-parser");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Config plugin to configure Windows-specific permissions and capabilities
 */
const withWindowsPermissions = (config, permissionsConfig = {}) => {
    var _a, _b, _c;
    // Add Windows-specific permissions configuration to the config
    if (!config.extra) {
        config.extra = {};
    }
    // Default capabilities that most apps need
    const defaultCapabilities = ['internetClient'];
    // Map common Expo permissions to Windows capabilities
    const permissionMapping = {
        camera: ['webcam'],
        microphone: ['microphone'],
        location: ['location'],
        notifications: ['userNotificationListener'],
        contacts: ['contacts'],
        calendar: ['appointments'],
        photos: ['picturesLibrary'],
        videos: ['videosLibrary'],
        music: ['musicLibrary'],
        documents: ['documentsLibrary'],
        bluetooth: ['bluetooth'],
        wifi: ['wiFiControl'],
    };
    // Collect capabilities from Expo permissions
    const autoCapabilities = [];
    const permissions = config.permissions;
    if (permissions) {
        for (const [permission, enabled] of Object.entries(permissions)) {
            if (enabled && permissionMapping[permission]) {
                autoCapabilities.push(...permissionMapping[permission]);
            }
        }
    }
    // Combine all capabilities
    const allCapabilities = [
        ...defaultCapabilities,
        ...autoCapabilities,
        ...(permissionsConfig.capabilities || []),
    ];
    // Remove duplicates
    const uniqueCapabilities = [...new Set(allCapabilities)];
    // Store Windows permissions configuration for later use during prebuild
    config.extra.windowsPermissions = {
        capabilities: uniqueCapabilities,
        deviceCapabilities: permissionsConfig.deviceCapabilities || [],
        restrictedCapabilities: permissionsConfig.restrictedCapabilities || [],
        customCapabilities: permissionsConfig.customCapabilities || [],
        privacyPolicy: {
            url: (_a = permissionsConfig.privacyPolicy) === null || _a === void 0 ? void 0 : _a.url,
            contact: (_b = permissionsConfig.privacyPolicy) === null || _b === void 0 ? void 0 : _b.contact,
        },
        // Include mapping information for debugging
        autoDetectedCapabilities: autoCapabilities,
        permissionMapping,
    };
    console.log('🔐 Applying Windows permissions configuration...');
    console.log(`   Capabilities: ${uniqueCapabilities.join(', ')}`);
    if ((_c = permissionsConfig.deviceCapabilities) === null || _c === void 0 ? void 0 : _c.length) {
        console.log(`   Device Capabilities: ${permissionsConfig.deviceCapabilities.join(', ')}`);
    }
    // Add dangerous mod to actually modify the manifest file
    return (0, config_plugins_1.withDangerousMod)(config, [
        'windows',
        async (config) => {
            var _a;
            const projectRoot = config.modRequest.projectRoot;
            const windowsDir = path.join(projectRoot, 'windows');
            // Find the Package.appxmanifest file
            const manifestPath = findWindowsManifest(windowsDir);
            if (!manifestPath) {
                console.warn('⚠️  Windows Package.appxmanifest not found, skipping permissions configuration');
                return config;
            }
            console.log('🔐 Modifying Windows manifest permissions...');
            // Read and parse the manifest
            const manifestContent = fs.readFileSync(manifestPath, 'utf8');
            const parser = new fast_xml_parser_1.XMLParser({
                ignoreAttributes: false,
                attributeNamePrefix: '@_',
                textNodeName: '#text',
            });
            const manifest = parser.parse(manifestContent);
            // Apply permissions modifications
            const modifiedManifest = await modifyWindowsPermissions(manifest, (_a = config.extra) === null || _a === void 0 ? void 0 : _a.windowsPermissions);
            // Write back the modified manifest
            const builder = new fast_xml_parser_1.XMLBuilder({
                ignoreAttributes: false,
                attributeNamePrefix: '@_',
                textNodeName: '#text',
                format: true,
            });
            const modifiedContent = builder.build(modifiedManifest);
            fs.writeFileSync(manifestPath, modifiedContent);
            console.log('✅ Windows permissions configuration applied');
            return config;
        },
    ]);
};
exports.withWindowsPermissions = withWindowsPermissions;
/**
 * Find Windows Package.appxmanifest file
 */
function findWindowsManifest(windowsDir) {
    if (!fs.existsSync(windowsDir)) {
        return null;
    }
    // Look for Package.appxmanifest in common locations
    const possiblePaths = [
        path.join(windowsDir, 'Package.appxmanifest'),
        path.join(windowsDir, 'ReactNativeApp', 'Package.appxmanifest'),
    ];
    for (const manifestPath of possiblePaths) {
        if (fs.existsSync(manifestPath)) {
            return manifestPath;
        }
    }
    return null;
}
/**
 * Modify Windows manifest with permissions and capabilities
 */
async function modifyWindowsPermissions(manifest, permissionsConfig) {
    if (!permissionsConfig) {
        return manifest;
    }
    // Get the Package element
    const packageElement = manifest.Package;
    if (!packageElement) {
        throw new Error('Invalid Windows manifest: Package element not found');
    }
    // Initialize Capabilities section if it doesn't exist
    if (!packageElement.Capabilities) {
        packageElement.Capabilities = {};
    }
    // Add standard capabilities
    if (permissionsConfig.capabilities && permissionsConfig.capabilities.length > 0) {
        const capabilities = permissionsConfig.capabilities.map((cap) => ({
            '@_Name': cap,
        }));
        if (Array.isArray(packageElement.Capabilities.Capability)) {
            packageElement.Capabilities.Capability.push(...capabilities);
        }
        else if (packageElement.Capabilities.Capability) {
            packageElement.Capabilities.Capability = [packageElement.Capabilities.Capability, ...capabilities];
        }
        else {
            packageElement.Capabilities.Capability = capabilities;
        }
    }
    // Add device capabilities
    if (permissionsConfig.deviceCapabilities && permissionsConfig.deviceCapabilities.length > 0) {
        const deviceCapabilities = permissionsConfig.deviceCapabilities.map((cap) => ({
            '@_Name': cap,
        }));
        if (Array.isArray(packageElement.Capabilities.DeviceCapability)) {
            packageElement.Capabilities.DeviceCapability.push(...deviceCapabilities);
        }
        else if (packageElement.Capabilities.DeviceCapability) {
            packageElement.Capabilities.DeviceCapability = [packageElement.Capabilities.DeviceCapability, ...deviceCapabilities];
        }
        else {
            packageElement.Capabilities.DeviceCapability = deviceCapabilities;
        }
    }
    // Add restricted capabilities
    if (permissionsConfig.restrictedCapabilities && permissionsConfig.restrictedCapabilities.length > 0) {
        const restrictedCapabilities = permissionsConfig.restrictedCapabilities.map((cap) => ({
            '@_Name': cap,
        }));
        if (Array.isArray(packageElement.Capabilities.rescap_Capability)) {
            packageElement.Capabilities.rescap_Capability.push(...restrictedCapabilities);
        }
        else if (packageElement.Capabilities.rescap_Capability) {
            packageElement.Capabilities.rescap_Capability = [packageElement.Capabilities.rescap_Capability, ...restrictedCapabilities];
        }
        else {
            packageElement.Capabilities.rescap_Capability = restrictedCapabilities;
        }
    }
    // Add custom capabilities
    if (permissionsConfig.customCapabilities && permissionsConfig.customCapabilities.length > 0) {
        const customCapabilities = permissionsConfig.customCapabilities.map((cap) => ({
            '@_Name': cap.name,
            '@_Publisher': cap.publisher || 'CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US',
        }));
        if (Array.isArray(packageElement.Capabilities.uap4_CustomCapability)) {
            packageElement.Capabilities.uap4_CustomCapability.push(...customCapabilities);
        }
        else if (packageElement.Capabilities.uap4_CustomCapability) {
            packageElement.Capabilities.uap4_CustomCapability = [packageElement.Capabilities.uap4_CustomCapability, ...customCapabilities];
        }
        else {
            packageElement.Capabilities.uap4_CustomCapability = customCapabilities;
        }
    }
    return manifest;
}
//# sourceMappingURL=withWindowsPermissions.js.map