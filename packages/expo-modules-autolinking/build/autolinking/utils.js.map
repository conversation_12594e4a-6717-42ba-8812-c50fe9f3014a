{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/autolinking/utils.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AAIxB,gDAAgD;AAChD,IAAI,gBAAgB,GAAQ,IAAI,CAAC;AAEjC;;;GAGG;AACH,SAAS,mBAAmB;IAC1B,IAAI,CAAC,gBAAgB,EAAE;QACrB,IAAI;YACF,0CAA0C;YAC1C,MAAM,eAAe,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAC3D,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;SACrD;QAAC,OAAO,aAAa,EAAE;YACtB,IAAI;gBACF,qDAAqD;gBACrD,MAAM,UAAU,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;gBAClE,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;aAChD;YAAC,MAAM;gBACN,uDAAuD;gBACvD,gBAAgB,GAAG;oBACjB,qBAAqB,EAAE,GAAG,EAAE,CAAC,EAAE;oBAC/B,WAAW,EAAE,GAAG,EAAE,CAAC,SAAS;iBAC7B,CAAC;aACH;SACF;KACF;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAgB,mCAAmC,CAAC,QAAoC;IACtF,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC;IAEvC,iCAAiC;IACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAEpD,IAAI,YAAY,EAAE;QAChB,4DAA4D;QAC5D,IAAI,YAAY,CAAC,yBAAyB,EAAE;YAC1C,OAAO,YAAY,CAAC,yBAAyB,CAAC;SAC/C;KACF;IAED,uCAAuC;IACvC,OAAO,gCAAgC,CAAC,QAAQ,CAAC,CAAC;AACpD,CAAC;AAfD,kFAeC;AAED;;;GAGG;AACH,SAAS,gCAAgC,CAAC,QAAoC;IAC5E,QAAQ,QAAQ,EAAE;QAChB,KAAK,KAAK,CAAC;QACX,KAAK,OAAO,CAAC;QACb,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,OAAO,CAAC,oBAAoB,CAAC,CAAC;QACvC,KAAK,SAAS;YACZ,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACzC,KAAK,UAAU;YACb,OAAO,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC1C;YACE,OAAO,IAAI,CAAC;KACf;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAAC,WAAmB,EAAE,WAAmB;IAC7E,8DAA8D;IAC9D,iEAAiE;IACjE,kEAAkE;IAClE,qDAAqD;IACrD,gDAAgD;IAChD,4DAA4D;IAC5D,+DAA+D;IAC/D,2CAA2C;IAC3C,MAAM,wBAAwB,GAAG,cAAI,CAAC,IAAI,CACxC,WAAW,EACX,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC;KAC/G,CAAC;IACF,MAAM,qBAAqB,GAAG,cAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,KAAK,cAAc,CAAC;IACzF,OAAO,qBAAqB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC;AACjE,CAAC;AAfD,wDAeC", "sourcesContent": ["import path from 'path';\n\nimport { SupportedPlatform } from '../types';\n\n// Dynamic import to avoid circular dependencies\nlet platformRegistry: any = null;\n\n/**\n * Get the platform registry instance.\n * This is loaded dynamically to avoid circular dependencies.\n */\nfunction getPlatformRegistry() {\n  if (!platformRegistry) {\n    try {\n      // Try new platform registry package first\n      const registryPackage = require('@expo/platform-registry');\n      platformRegistry = registryPackage.platformRegistry;\n    } catch (registryError) {\n      try {\n        // Fallback to CLI package for backward compatibility\n        const cliPackage = require('@expo/cli/src/core/PlatformRegistry');\n        platformRegistry = cliPackage.platformRegistry;\n      } catch {\n        // If neither package is available, use a mock registry\n        platformRegistry = {\n          getAvailablePlatforms: () => [],\n          getPlatform: () => undefined,\n        };\n      }\n    }\n  }\n  return platformRegistry;\n}\n\nexport function getLinkingImplementationForPlatform(platform: SupportedPlatform | string) {\n  const registry = getPlatformRegistry();\n\n  // Try to get platform data first\n  const platformData = registry.getPlatform(platform);\n\n  if (platformData) {\n    // If platform has custom autolinking implementation, use it\n    if (platformData.autolinkingImplementation) {\n      return platformData.autolinkingImplementation;\n    }\n  }\n\n  // Fallback to built-in implementations\n  return getBuiltInPlatformImplementation(platform);\n}\n\n/**\n * Get autolinking implementation for built-in platforms.\n * This is used as a fallback when platform providers are not available.\n */\nfunction getBuiltInPlatformImplementation(platform: SupportedPlatform | string) {\n  switch (platform) {\n    case 'ios':\n    case 'macos':\n    case 'tvos':\n    case 'apple':\n      return require('../platforms/apple');\n    case 'android':\n      return require('../platforms/android');\n    case 'devtools':\n      return require('../platforms/devtools');\n    default:\n      return null;\n  }\n}\n\n/**\n * Get the possible path to the pnpm isolated modules folder.\n */\nexport function getIsolatedModulesPath(packagePath: string, packageName: string): string | null {\n  // Check if the project is using isolated modules, by checking\n  // if the parent dir of `packagePath` is a `node_modules` folder.\n  // Isolated modules installs dependencies in small groups such as:\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/@expo/cli\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo-application\n  // When isolated modules are detected, expand the `searchPaths`\n  // to include possible nested dependencies.\n  const maybeIsolatedModulesPath = path.join(\n    packagePath,\n    packageName.startsWith('@') && packageName.includes('/') ? '../..' : '..' // scoped packages are nested deeper\n  );\n  const isIsolatedModulesPath = path.basename(maybeIsolatedModulesPath) === 'node_modules';\n  return isIsolatedModulesPath ? maybeIsolatedModulesPath : null;\n}\n"]}