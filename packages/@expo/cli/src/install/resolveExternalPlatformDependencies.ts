import { getConfig } from '@expo/config';

import { platformRegistry } from '../core/PlatformRegistry';
import * as Log from '../log';

const debug = require('debug')('expo:install:external-platforms') as typeof console.log;

/**
 * Resolve additional dependencies required by external platforms.
 *
 * This function checks all external platforms in the project and asks them
 * to resolve any additional dependencies that should be installed alongside
 * the requested packages.
 *
 * @param projectRoot Project root directory
 * @param packages List of packages being installed
 * @param sdkVersion Current Expo SDK version
 * @returns Promise resolving to additional packages that should be installed
 */
export async function resolveExternalPlatformDependencies(
  projectRoot: string,
  packages: string[],
  sdkVersion: string
): Promise<string[]> {
  try {
    // Get project configuration to determine which platforms are enabled
    const { exp } = getConfig(projectRoot, { skipPlugins: true });
    const configuredPlatforms = exp.platforms || [];

    // Filter to only external platforms (not ios, android, web)
    const externalPlatforms = configuredPlatforms.filter(
      (platform) => !['ios', 'android', 'web'].includes(platform)
    );

    if (externalPlatforms.length === 0) {
      debug('No external platforms configured, skipping dependency resolution');
      return [];
    }

    debug(`Resolving dependencies for external platforms: ${externalPlatforms.join(', ')}`);

    const additionalPackages: string[] = [];

    // Process each external platform
    for (const platformName of externalPlatforms) {
      const platformData = platformRegistry.getPlatform(platformName);

      if (!platformData) {
        debug(`Platform ${platformName} not found in registry, skipping`);
        continue;
      }

      if (!platformData.dependencyResolver) {
        debug(`Platform ${platformName} does not provide dependency resolver, skipping`);
        continue;
      }

      try {
        debug(`Resolving dependencies for platform: ${platformName}`);
        const platformDependencies = await platformData.dependencyResolver.resolveDependencies(
          packages,
          sdkVersion
        );

        if (platformDependencies.length > 0) {
          debug(
            `Platform ${platformName} resolved dependencies: ${platformDependencies.join(', ')}`
          );
          additionalPackages.push(...platformDependencies);
        } else {
          debug(`Platform ${platformName} resolved no additional dependencies`);
        }
      } catch (error: any) {
        // Log warning but don't fail the entire install process
        Log.warn(`Failed to resolve dependencies for platform ${platformName}: ${error.message}`);
        debug(`Dependency resolution error for ${platformName}:`, error);
      }
    }

    // Remove duplicates and return
    const uniquePackages = [...new Set(additionalPackages)];

    if (uniquePackages.length > 0) {
      debug(`Total additional packages resolved: ${uniquePackages.join(', ')}`);
    } else {
      debug('No additional packages resolved by external platforms');
    }

    return uniquePackages;
  } catch (error: any) {
    // Log warning but don't fail the install process
    Log.warn(`Failed to resolve external platform dependencies: ${error.message}`);
    debug('External platform dependency resolution error:', error);
    return [];
  }
}

/**
 * Configure autolinking for external platforms after package installation.
 *
 * This function notifies all external platforms that packages have been installed
 * and gives them a chance to configure platform-specific autolinking.
 *
 * @param projectRoot Project root directory
 * @param packages List of packages that were installed
 */
export async function configureExternalPlatformAutolinking(
  projectRoot: string,
  packages: string[]
): Promise<void> {
  try {
    // Get project configuration to determine which platforms are enabled
    const { exp } = getConfig(projectRoot, { skipPlugins: true });
    const configuredPlatforms = exp.platforms || [];

    // Filter to only external platforms (not ios, android, web)
    const externalPlatforms = configuredPlatforms.filter(
      (platform) => !['ios', 'android', 'web'].includes(platform)
    );

    if (externalPlatforms.length === 0) {
      debug('No external platforms configured, skipping autolinking configuration');
      return;
    }

    debug(`Configuring autolinking for external platforms: ${externalPlatforms.join(', ')}`);

    // Process each external platform
    for (const platformName of externalPlatforms) {
      const platformData = platformRegistry.getPlatform(platformName);

      if (!platformData) {
        debug(`Platform ${platformName} not found in registry, skipping`);
        continue;
      }

      if (!platformData.dependencyResolver?.configureAutolinking) {
        debug(`Platform ${platformName} does not provide autolinking configuration, skipping`);
        continue;
      }

      try {
        debug(`Configuring autolinking for platform: ${platformName}`);
        await platformData.dependencyResolver.configureAutolinking(projectRoot, packages);
        debug(`Successfully configured autolinking for platform: ${platformName}`);
      } catch (error: any) {
        // Log warning but don't fail the entire install process
        Log.warn(`Failed to configure autolinking for platform ${platformName}: ${error.message}`);
        debug(`Autolinking configuration error for ${platformName}:`, error);
      }
    }
  } catch (error: any) {
    // Log warning but don't fail the install process
    Log.warn(`Failed to configure external platform autolinking: ${error.message}`);
    debug('External platform autolinking configuration error:', error);
  }
}

/**
 * Check if any external platforms are configured in the project.
 *
 * @param projectRoot Project root directory
 * @returns True if external platforms are configured
 */
export function hasExternalPlatforms(projectRoot: string): boolean {
  try {
    const { exp } = getConfig(projectRoot, { skipPlugins: true });
    const configuredPlatforms = exp.platforms || [];

    return configuredPlatforms.some((platform) => !['ios', 'android', 'web'].includes(platform));
  } catch (error) {
    debug('Failed to check for external platforms:', error);
    return false;
  }
}
