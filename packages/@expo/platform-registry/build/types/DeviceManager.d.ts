/**
 * Base interface for device resolution properties.
 * External platforms can extend this with platform-specific options.
 */
export interface BaseResolveDeviceProps<TDevice> {
    /** Should prompt user to select device if multiple found */
    prompt?: boolean;
    /** Device identifier to target specifically */
    device?: string | boolean;
    /** Additional platform-specific device resolution options */
    [key: string]: any;
}
/**
 * External platform device manager constructor.
 * External platforms should provide a DeviceManager class that extends the base DeviceManager.
 * The class must implement a static resolveAsync method for device resolution.
 */
export interface ExternalPlatformDeviceManagerConstructor<TDevice> {
    new (device: TDevice): any;
    resolveAsync(options?: BaseResolveDeviceProps<TDevice>): Promise<any>;
}
/**
 * External platform manager constructor.
 * Creates a PlatformManager instance that extends Expo's base class.
 */
export type ExternalPlatformManagerConstructor<TDevice> = new (projectRoot: string, options: {
    getDevServerUrl: () => string | null;
    getExpoGoUrl: () => string;
    getRedirectUrl: () => string | null;
    getCustomRuntimeUrl: (props?: {
        scheme?: string;
    }) => string | null;
    resolveDeviceAsync: (options?: BaseResolveDeviceProps<TDevice>) => Promise<any>;
}) => any;
/**
 * External platform app ID resolver constructor.
 */
export type ExternalPlatformAppIdResolverConstructor = new (projectRoot: string) => any;
//# sourceMappingURL=DeviceManager.d.ts.map