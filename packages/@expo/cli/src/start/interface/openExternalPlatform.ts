import { platformRegistry } from '../../core/PlatformRegistry';
import * as Log from '../../log';
import { AbortCommandError, CommandError } from '../../utils/errors';
import { DevServerManager } from '../server/DevServerManager';

/**
 * Opens an external platform using its device manager integration.
 * This provides proper device selection and URL opening for external platforms
 * that implement the device manager interface.
 */
export async function openExternalPlatformAsync(
  devServerManager: DevServerManager,
  platform: string,
  options: { shouldPrompt?: boolean } = {}
): Promise<{ url: string }> {
  const platformData = platformRegistry.getPlatform(platform);

  if (!platformData) {
    throw new CommandError('PLATFORM_NOT_FOUND', `Platform "${platform}" not found in registry`);
  }

  if (!platformData.deviceManagerConstructor) {
    throw new CommandError(
      'MISSING_DEVICE_MANAGER',
      `Platform "${platform}" does not provide device management`
    );
  }

  if (!platformData.platformManagerConstructor) {
    throw new CommandError(
      'MISSING_PLATFORM_MANAGER',
      `Platform "${platform}" does not provide platform manager`
    );
  }

  try {
    // Get the dev server URL
    const devServerUrl = devServerManager.getDefaultDevServer()?.getDevServerUrl();
    if (!devServerUrl) {
      throw new CommandError('DEV_SERVER_NOT_RUNNING', 'Development server is not running');
    }

    // Create platform manager instance
    const platformManager = new platformData.platformManagerConstructor(
      devServerManager.projectRoot,
      {
        getDevServerUrl: () => devServerUrl,
        getExpoGoUrl: () => {
          // External platforms typically don't use Expo Go, but provide a fallback
          return devServerUrl;
        },
        getRedirectUrl: () => {
          // Get redirect URL for native disambiguation
          return devServerManager.getDefaultDevServer()?.getRedirectUrl() || null;
        },
        getCustomRuntimeUrl: (props?: { scheme?: string }) => {
          // Get custom runtime URL for dev client
          const server = devServerManager.getDefaultDevServer();
          if (server && server.isDevClient) {
            return server.getUrlCreator().constructDevClientUrl(props) || null;
          }
          return null;
        },
        resolveDeviceAsync: platformData.deviceManagerConstructor.resolveAsync,
      }
    );

    // Determine runtime type
    const runtime = devServerManager.options.devClient ? 'custom' : 'expo';

    // Open the platform with device selection
    const result = await platformManager.openAsync(
      { runtime },
      { shouldPrompt: options.shouldPrompt }
    );

    Log.log(`✅ Opened ${platformData.displayName || platform} successfully`);

    return result;
  } catch (error: any) {
    // Provide helpful error messages for common issues
    if (error.message.includes('No devices found')) {
      throw new CommandError(
        'NO_DEVICES_FOUND',
        `No ${platformData.displayName || platform} devices found.\n\n` +
          `Make sure you have:\n` +
          `• ${platformData.displayName || platform} development environment set up\n` +
          `• Devices/simulators available and running\n` +
          `• Platform-specific dependencies installed\n\n` +
          `Check the ${platform} platform documentation for setup instructions.`
      );
    }

    if (error.message.includes('Device selection cancelled')) {
      throw new AbortCommandError();
    }

    // Re-throw other errors with platform context
    throw new CommandError(
      'EXTERNAL_PLATFORM_OPEN_FAILED',
      `Failed to open ${platformData.displayName || platform}:\n${error.message}`
    );
  }
}
