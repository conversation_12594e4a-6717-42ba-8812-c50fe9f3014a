import type { ConfigT } from 'metro-config';
/**
 * Extends Metro configuration to support external platforms.
 * This function attempts to integrate with the platform registry when available,
 * falling back gracefully when the CLI is not available.
 *
 * This function is safe to call multiple times - it will detect if external platforms
 * have already been added and avoid duplicates.
 */
export declare function withExternalPlatforms(config: ConfigT, platformData?: any): ConfigT;
