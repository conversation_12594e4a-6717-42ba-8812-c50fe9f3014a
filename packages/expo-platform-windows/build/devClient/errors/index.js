"use strict";
/**
 * Windows Error Boundary Configurations
 *
 * Provides configuration for Windows-specific error boundaries.
 * The actual React components would be implemented by the Expo development client.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWindowsErrorBoundaryConfig = exports.getWindowsErrorBoundaryConfigs = exports.winAppSDKErrorBoundaryConfig = exports.uwpErrorBoundaryConfig = exports.windowsErrorBoundaryConfig = void 0;
/**
 * Windows Error Boundary Configuration
 */
exports.windowsErrorBoundaryConfig = {
    type: 'windows',
    name: 'Windows Error Boundary',
    description: 'Handles general Windows application errors with Windows-specific context logging',
    fallbackComponent: 'expo-platform-windows/devClient/components/WindowsErrorFallback',
    errorHandler: 'expo-platform-windows/devClient/handlers/windowsErrorHandler',
    contextProvider: 'expo-platform-windows/devClient/context/windowsErrorContext',
};
/**
 * UWP Error Boundary Configuration
 */
exports.uwpErrorBoundaryConfig = {
    type: 'uwp',
    name: 'UWP Error Boundary',
    description: 'Handles UWP-specific errors with package and capability context',
    fallbackComponent: 'expo-platform-windows/devClient/components/UWPErrorFallback',
    errorHandler: 'expo-platform-windows/devClient/handlers/uwpErrorHandler',
    contextProvider: 'expo-platform-windows/devClient/context/uwpErrorContext',
};
/**
 * WinAppSDK Error Boundary Configuration
 */
exports.winAppSDKErrorBoundaryConfig = {
    type: 'winappsdk',
    name: 'WinAppSDK Error Boundary',
    description: 'Handles Windows App SDK errors with framework-specific diagnostics',
    fallbackComponent: 'expo-platform-windows/devClient/components/WinAppSDKErrorFallback',
    errorHandler: 'expo-platform-windows/devClient/handlers/winAppSDKErrorHandler',
    contextProvider: 'expo-platform-windows/devClient/context/winAppSDKErrorContext',
};
/**
 * Get all Windows error boundary configurations
 */
function getWindowsErrorBoundaryConfigs() {
    return [
        exports.windowsErrorBoundaryConfig,
        exports.uwpErrorBoundaryConfig,
        exports.winAppSDKErrorBoundaryConfig,
    ];
}
exports.getWindowsErrorBoundaryConfigs = getWindowsErrorBoundaryConfigs;
/**
 * Get error boundary configuration by type
 */
function getWindowsErrorBoundaryConfig(type) {
    switch (type) {
        case 'windows':
            return exports.windowsErrorBoundaryConfig;
        case 'uwp':
            return exports.uwpErrorBoundaryConfig;
        case 'winappsdk':
            return exports.winAppSDKErrorBoundaryConfig;
        default:
            return exports.windowsErrorBoundaryConfig;
    }
}
exports.getWindowsErrorBoundaryConfig = getWindowsErrorBoundaryConfig;
//# sourceMappingURL=index.js.map