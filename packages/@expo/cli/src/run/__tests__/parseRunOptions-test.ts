import { parseRunOptions, getProjectRootFromArgs } from '../parseRunOptions';

describe('parseRunOptions', () => {
  it('should parse basic run options', () => {
    const argv = ['--port', '8081', '--device', '--release'];
    const options = parseRunOptions(argv);

    expect(options).toEqual({
      port: 8081,
      device: true,
      buildCache: true,
      install: true,
      bundler: true,
      variant: 'release',
      configuration: 'Release',
    });
  });

  it('should parse negative flags correctly', () => {
    const argv = ['--no-build-cache', '--no-install', '--no-bundler'];
    const options = parseRunOptions(argv);

    expect(options).toEqual({
      buildCache: false,
      install: false,
      bundler: false,
    });
  });

  it('should parse string options', () => {
    const argv = ['--binary', '/path/to/app.apk', '--variant', 'debug', '--scheme', 'MyScheme'];
    const options = parseRunOptions(argv);

    expect(options).toEqual({
      binary: '/path/to/app.apk',
      variant: 'debug',
      scheme: 'MyScheme',
      buildCache: true,
      install: true,
      bundler: true,
    });
  });

  it('should handle platform-specific options', () => {
    const argv = ['--arch', 'x64', '--target', 'device-id', '--custom-flag'];
    const options = parseRunOptions(argv);

    expect(options).toEqual({
      arch: 'x64',
      target: 'device-id',
      'custom-flag': true,
      buildCache: true,
      install: true,
      bundler: true,
    });
  });

  it('should handle empty argv', () => {
    const options = parseRunOptions([]);

    expect(options).toEqual({
      buildCache: true,
      install: true,
      bundler: true,
    });
  });

  it('should handle mixed options', () => {
    const argv = [
      '--port',
      '3000',
      '--no-bundler',
      '--device',
      '--configuration',
      'Release',
      '--custom-option',
      'value',
      '--flag-only',
    ];
    const options = parseRunOptions(argv);

    expect(options).toEqual({
      port: 3000,
      bundler: false,
      device: true,
      configuration: 'Release',
      'custom-option': 'value',
      'flag-only': true,
      buildCache: true,
      install: true,
    });
  });
});

describe('getProjectRootFromArgs', () => {
  it('should return first non-flag argument as project root', () => {
    const argv = ['/path/to/project', '--port', '8081'];
    const projectRoot = getProjectRootFromArgs(argv);

    expect(projectRoot).toBe('/path/to/project');
  });

  it('should return current directory when no non-flag arguments', () => {
    const argv = ['--port', '8081', '--device'];
    const projectRoot = getProjectRootFromArgs(argv);

    expect(projectRoot).toBe(process.cwd());
  });

  it('should handle empty argv', () => {
    const projectRoot = getProjectRootFromArgs([]);

    expect(projectRoot).toBe(process.cwd());
  });
});
