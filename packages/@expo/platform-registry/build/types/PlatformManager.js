"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalPlatformPrerequisite = void 0;
/**
 * Abstract base class for external platform prerequisites.
 * External platforms should extend this to provide health checking.
 */
class ExternalPlatformPrerequisite {
    constructor(platform) {
        this.platform = platform;
    }
    /**
     * Run all prerequisite checks and throw if any fail.
     * This is the main entry point called by the doctor command.
     */
    async assertAsync() {
        const checks = [
            { name: 'Development Environment', check: () => this.checkDevelopmentEnvironment() },
            { name: 'System Requirements', check: () => this.checkSystemRequirements() },
            { name: 'Platform Tools', check: () => this.checkPlatformTools() },
        ];
        const failures = [];
        for (const { name, check } of checks) {
            try {
                const result = await check();
                if (!result) {
                    failures.push(name);
                }
            }
            catch (error) {
                failures.push(`${name}: ${error.message}`);
            }
        }
        if (failures.length > 0) {
            const instructions = this.getInstallationInstructions();
            const instructionText = instructions.length > 0
                ? `\n\nTo fix these issues:\n${instructions.map((i) => `  • ${i}`).join('\n')}`
                : '';
            throw new Error(`Platform "${this.platform}" prerequisites not met:\n` +
                `${failures.map((f) => `  ✗ ${f}`).join('\n')}${instructionText}`);
        }
    }
}
exports.ExternalPlatformPrerequisite = ExternalPlatformPrerequisite;
//# sourceMappingURL=PlatformManager.js.map