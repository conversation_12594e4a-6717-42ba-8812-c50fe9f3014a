{"version": 3, "file": "WindowsPlatformManager.d.ts", "sourceRoot": "", "sources": ["../../src/platformManager/WindowsPlatformManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAEH,OAAO,EAAE,aAAa,EAAE,MAAM,mDAAmD,CAAC;AAClF,OAAO,EACL,eAAe,EACf,qBAAqB,EACrB,sBAAsB,EACvB,MAAM,qDAAqD,CAAC;AAY7D,OAAO,EAAwB,aAAa,EAAE,MAAM,uCAAuC,CAAC;AAM5F,MAAM,WAAW,mBAAmB;IAClC,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,0BAA2B,SAAQ,mBAAmB;IAErE,IAAI,CAAC,EAAE,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC;IAC/B,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,aAAa,GAAG,eAAe,CAAC;IACtE,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED;;;;;GAKG;AACH,qBAAa,sBAAuB,SAAQ,eAAe,CACzD,aAAa,EACb,0BAA0B,GAAG,qBAAqB,CACnD;gBAEG,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE;QACP,eAAe,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC;QACrC,YAAY,EAAE,MAAM,MAAM,CAAC;QAC3B,cAAc,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC;QACpC,mBAAmB,EAAE,CAAC,KAAK,CAAC,EAAE;YAAE,MAAM,CAAC,EAAE,MAAM,CAAA;SAAE,KAAK,MAAM,GAAG,IAAI,CAAC;QACpE,kBAAkB,EAAE,CAClB,OAAO,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,KACrD,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;KAC5C;IAOH;;OAEG;IACG,SAAS,CACb,OAAO,EACH;QAAE,OAAO,EAAE,MAAM,GAAG,KAAK,CAAA;KAAE,GAC3B;QAAE,OAAO,EAAE,QAAQ,CAAC;QAAC,KAAK,CAAC,EAAE,OAAO,CAAC,0BAA0B,GAAG,qBAAqB,CAAC,CAAA;KAAE,EAC9F,eAAe,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,GAC/D,OAAO,CAAC;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC;IAkB3B;;OAEG;YACW,cAAc;IAkB5B;;OAEG;IACH,oBAAoB,IAAI,0BAA0B;IAclD;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAAC,QAAQ,EAAE,MAAM,EAAE,CAAA;KAAE,CAAC;IA4FhG;;OAEG;YACW,mBAAmB;IAoCjC;;OAEG;YACW,mBAAmB;IAoCjC;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAoBxB;;OAEG;YACW,aAAa;IA2C3B;;OAEG;YACW,cAAc;IA4B5B;;OAEG;YACW,gCAAgC;IAiB9C;;OAEG;YACW,iBAAiB;IAgD/B;;OAEG;YACW,aAAa;IAqB3B;;OAEG;YACW,qBAAqB;IAenC;;OAEG;IACH,OAAO,CAAC,oBAAoB;CAG7B"}